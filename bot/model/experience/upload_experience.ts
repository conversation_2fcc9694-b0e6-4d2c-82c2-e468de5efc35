// 对话场景经验
import { Document } from 'langchain/document'
import { LLM } from '../../lib/ai/llm/LLM'
import { SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { XMLHelper } from '../../lib/xml/xml'
import { UUID } from '../../lib/uuid/uuid'

export interface IChatExperience {
    chatHistory: string
    scene?: string
    chatId?: string
    job?: string
    minValidSlotCount?: number
    maxValidSlotCount?: number
    meditationExperience?: string
    experience: string
}

export interface IInsightsExperience {
    job: string
    insights: string
}

export class UploadExperience {

  private static chatExpIndex = 'moer_experience'



  public static async uploadChatExperience(chatExp: IChatExperience[]) {


    const docs: Document[] = await Promise.all(chatExp.map(async (exp) => {
      const scene = await UploadExperience.summaryScene(exp.chatHistory, UUID.v4())
      return {
        pageContent: scene.summary,
        metadata: {
          job: exp.job,
          minValidSlotCount: exp.minValidSlotCount,
          maxValidSlotCount: exp.maxValidSlotCount,
          meditationExperience: exp.meditationExperience,
          strategy: exp.experience,
          scene: scene.scene,
          chatHistory: exp.chatHistory
        }
      }
    }))

    // await RAGHelper.addDocuments(this.chatExpIndex, docs)
  }


  public static async summaryScene(chatHistoryStr: string, roundId: string) {
    const promptTemplate = SystemMessagePromptTemplate.fromTemplate(`# 任务
- 识别客户发言下的聊天场景，**优先考虑客户最后一句话的场景**（只能选择一个:描述痛点，介绍自己过往经历，描述冥想经验，描述冥想目标，咨询冥想问题，咨询其他问题，质疑真人，询问回放，描述学习进度，咨询课程安排，描述课程感受，普通对话）
- 使用简短的话概括下面的聊天场景，多关注客户的发言(使用客户作主语)
# 聊天记录
${chatHistoryStr}

# 输出规则
<scene></scene>
<summary></summary>`)
    const llm = new LLM({
      model: 'gpt-5-mini',
      meta: {
        round_id: roundId
      }
    })
    const res = await llm.predict(promptTemplate, { chatHistoryStr })

    let scene = XMLHelper.extractContent(res, 'scene')
    if (!scene) scene = ''
    let summary = XMLHelper.extractContent(res, 'summary')
    if (!summary) summary = ''

    return { scene, summary }
  }



}