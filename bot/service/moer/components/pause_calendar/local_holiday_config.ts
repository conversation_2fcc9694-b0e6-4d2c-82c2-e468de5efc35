import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import isoWeek from 'dayjs/plugin/isoWeek'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import minMax from 'dayjs/plugin/minMax'
import logger from '../../../../model/logger/logger'

dayjs.extend(isBetween)
dayjs.extend(isoWeek)
dayjs.extend(isSameOrBefore)
dayjs.extend(isSameOrAfter)
dayjs.extend(minMax)

export interface HolidayPeriod {
  startDate: string  // 格式: 'YYYY-MM-DD'
  endDate: string    // 格式: 'YYYY-MM-DD'
  reason?: string    // 假期原因
}

/**
 * 本地假期配置
 * 业务人员只需在这里添加假期时间段，系统会自动处理期数计算
 */
export class LocalHolidayConfig {
  /**
   * 假期配置列表
   * 添加新假期时，只需在这个数组中添加新的时间段即可
   */
  private static readonly HOLIDAY_PERIODS: HolidayPeriod[] = [
    {
      startDate: '2025-04-21',
      endDate: '2025-05-03',
      reason: '原硬编码假期'
    },

    {
      startDate: '2025-09-29',
      endDate: '2025-10-05',
      reason: '国庆假期'
    },

    // 示例：添加新假期的方法
    // {
    //   startDate: '2025-05-01',
    //   endDate: '2025-05-03',
    //   reason: '五一劳动节假期'
    // },
    // {
    //   startDate: '2025-10-01',
    //   endDate: '2025-10-07',
    //   reason: '国庆节假期'
    // }
  ]

  /**
   * 基准配置
   */
  private static readonly ANCHOR_DATE = dayjs('2024-01-29') // 第0期开始日期（开营日）
  private static readonly ANCHOR_PERIOD = 0 // 基准期数

  /**
   * 检查指定日期是否在假期内
   * @param date 检查日期
   * @returns 是否在假期内
   */
  public static isDateInHoliday(date: Date): boolean {
    const checkDate = dayjs(date)

    return this.HOLIDAY_PERIODS.some((holiday) => {
      return checkDate.isBetween(
        dayjs(holiday.startDate),
        dayjs(holiday.endDate),
        'day',
        '[]' // 包含边界
      )
    })
  }

  /**
   * 检查当前是否在假期内
   * @returns 是否在假期内
   */
  public static isCurrentlyInHoliday(): boolean {
    return this.isDateInHoliday(new Date())
  }

  /**
   * 获取当前假期信息（如果在假期内）
   * @returns 假期信息，如果不在假期内则返回null
   */
  public static getCurrentHolidayInfo(): {
    reason: string
    startDate: string
    endDate: string
  } | null {
    const today = dayjs()

    const currentHoliday = this.HOLIDAY_PERIODS.find((holiday) => {
      return today.isBetween(
        dayjs(holiday.startDate),
        dayjs(holiday.endDate),
        'day',
        '[]'
      )
    })

    if (!currentHoliday) {
      return null
    }

    return {
      reason: currentHoliday.reason || '假期',
      startDate: currentHoliday.startDate,
      endDate: currentHoliday.endDate
    }
  }

  /**
   * 计算原始周期号（不含任何假期"补偿"）
   * @param date 查询日期
   * @returns 原始期数
   */
  private static getOriginalPeriod(date: Date): number {
    const startDate = this.ANCHOR_DATE
    const startOfStartWeek = startDate.startOf('isoWeek')
    const startOfCurrentWeek = dayjs(date).startOf('isoWeek')

    // 计算周数差
    return startOfCurrentWeek.diff(startOfStartWeek, 'week')
  }

  /**
   * 计算被假期影响的周数
   * @param queryDate 查询日期
   * @returns 被暂停的周数
   */
  private static calculatePausedWeeks(queryDate: Date): number {
    const anchor = this.ANCHOR_DATE
    const query = dayjs(queryDate)

    let pausedWeeks = 0

    for (const holiday of this.HOLIDAY_PERIODS) {
      const holidayStart = dayjs(holiday.startDate)
      const holidayEnd = dayjs(holiday.endDate)

      // 只考虑在查询时间范围内的假期
      if (holidayEnd.isBefore(anchor) || holidayStart.isAfter(query)) {
        continue
      }

      // 计算假期与时间段的交集
      const intersectionStart = holidayStart.isAfter(anchor) ? holidayStart : anchor
      const intersectionEnd = holidayEnd.isBefore(query) ? holidayEnd : query

      if (intersectionStart.isSameOrBefore(intersectionEnd)) {
        // 计算假期跨越的周数
        const startWeek = intersectionStart.startOf('isoWeek')
        const endWeek = intersectionEnd.startOf('isoWeek')
        const weeksSpanned = endWeek.diff(startWeek, 'week') + 1

        pausedWeeks += weeksSpanned
      }
    }

    // logger.log('计算暂停周数', {
    //   queryDate: query.format('YYYY-MM-DD'),
    //   pausedWeeks,
    //   holidayCount: this.HOLIDAY_PERIODS.length
    // })

    return pausedWeeks
  }

  /**
   * 计算指定日期的课程期数
   * @param queryDate 查询日期
   * @returns 期数
   */
  public static calculatePeriod(queryDate: Date): number {
    const query = dayjs(queryDate)

    // 如果查询日期早于基准日期，返回0
    if (query.isBefore(this.ANCHOR_DATE)) {
      logger.warn('查询日期早于基准日期', {
        queryDate: query.format('YYYY-MM-DD'),
        anchorDate: this.ANCHOR_DATE.format('YYYY-MM-DD')
      })
      return 0
    }

    // 计算原始期数（按周计算）
    const originalPeriod = this.getOriginalPeriod(queryDate)

    // 计算被假期暂停的周数
    const pausedWeeks = this.calculatePausedWeeks(queryDate)

    // 最终期数 = 原始期数 - 暂停周数
    const finalPeriod = originalPeriod - pausedWeeks

    // logger.log('计算课程期数', {
    //   queryDate: query.format('YYYY-MM-DD'),
    //   originalPeriod,
    //   pausedWeeks,
    //   finalPeriod,
    //   anchorDate: this.ANCHOR_DATE.format('YYYY-MM-DD'),
    //   anchorPeriod: this.ANCHOR_PERIOD
    // })

    return Math.max(0, finalPeriod) // 确保不返回负数
  }

  /**
   * 获取当前期数
   * @returns 当前期数
   */
  public static getCurrentPeriod(date?: Date): number {
    return this.calculatePeriod(date ? date : new Date())
  }

  /**
   * 获取下一期期数
   * @returns 下一期期数
   */
  public static getNextPeriod(): number {
    const currentPeriod = this.getCurrentPeriod()
    return currentPeriod + 1
  }

  /**
   * 获取所有假期配置（用于调试）
   * @returns 假期配置列表
   */
  public static getAllHolidays(): HolidayPeriod[] {
    return [...this.HOLIDAY_PERIODS] // 返回副本，防止外部修改
  }

  /**
   * 获取基准配置信息
   */
  public static getAnchorInfo() {
    return {
      anchorDate: this.ANCHOR_DATE.format('YYYY-MM-DD'),
      anchorPeriod: this.ANCHOR_PERIOD,
      calculationMethod: 'weekly' // 按周计算
    }
  }

  /**
   * 预览期数计算结果（用于调试）
   * @param startDate 开始日期
   * @param endDate 结束日期
   * @returns 期数预览列表
   */
  public static previewPeriods(startDate: Date, endDate: Date): Array<{
    date: string
    period: number
    isHoliday: boolean
  }> {
    const results: Array<{ date: string; period: number; isHoliday: boolean }> = []
    let currentDate = dayjs(startDate)
    const end = dayjs(endDate)

    while (currentDate.isSameOrBefore(end)) {
      const period = this.calculatePeriod(currentDate.toDate())
      const isHoliday = this.isDateInHoliday(currentDate.toDate())

      results.push({
        date: currentDate.format('YYYY-MM-DD'),
        period,
        isHoliday
      })

      currentDate = currentDate.add(1, 'day')
    }

    return results
  }
}
