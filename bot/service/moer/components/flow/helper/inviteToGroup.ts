import { JuziAPI } from '../../../../../lib/juzi/api'
import { Config } from '../../../../../config/config'
import { HumanTransfer, HumanTransferType } from '../../human_transfer/human_transfer'
import { getChatId } from '../../../../../config/chat_id'
import { randomSleep } from '../../../../../lib/schedule/schedule'
import logger from '../../../../../model/logger/logger'
import { PrismaMongoClient } from '../../../../../model/mongodb/prisma'

interface IMoerEnterpriseConfig {
  notifyGroupId: string
  classGroupId: string
  classGroupIds?: string[]
  isGroupOwner?: boolean
  proxyGroupNotify?: boolean
  oldClassGroupId?: string
}

/**
 * 检查用户是否在新群或旧群中
 * @param userId 用户ID
 * @returns 如果用户在新群或旧群中返回true，否则返回false
 */
export async function isUserInAnyGroup(userId: string): Promise<boolean> {
  try {
    const botId = Config.setting.wechatConfig?.id as string
    const currentGroupId = Config.setting.wechatConfig?.classGroupId as string

    // 检查是否在当前群中
    const isInCurrentGroup = await JuziAPI.isInGroup({
      imBotId: botId,
      imRoomId: currentGroupId,
      imContactId: userId
    })

    if (isInCurrentGroup) {
      return true
    }

    // 获取账号配置，检查是否有旧群ID
    const config = await PrismaMongoClient.getConfigInstance().config.findFirst({
      where: {
        wechatId: botId,
        enterpriseName: 'moer'
      }
    })

    if (config) {
      const enterpriseConfig = config.enterpriseConfig as unknown as IMoerEnterpriseConfig

      // 如果有旧群ID，检查是否在旧群中
      if (enterpriseConfig.oldClassGroupId) {
        const isInOldGroup = await JuziAPI.isInGroup({
          imBotId: botId,
          imRoomId: enterpriseConfig.oldClassGroupId,
          imContactId: userId
        })

        if (isInOldGroup) {
          return true
        }
      }
    }

    return false
  } catch (error) {
    logger.error('检查用户是否在群中失败', error, userId)
    return false
  }
}

export async function inviteToGroup(userId: string, waitTime: number = 3 * 60 * 1000) {
  try {
    // 先检查用户是否已经在新群或旧群中
    const isInAnyGroup = await isUserInAnyGroup(userId)
    if (isInAnyGroup) {
      logger.log('用户已在群中，跳过拉群', userId)
      return
    }

    // 建议拉人进群时，每分钟不超过50次： 所以随机到 3 分钟内
    await randomSleep(100, waitTime)// 等待随机秒数

    const response = await JuziAPI.addToGroup({
      botUserId: Config.setting.wechatConfig?.botUserId as string,
      contactWxid: userId,
      roomWxid: Config.setting.wechatConfig?.classGroupId as string
    })

    // Check for errors in the response
    if (response.errcode !== undefined && response.errcode !== 0) {
      // Only handle errors that are not -8 or -9
      if (response.errcode !== -8 && response.errcode !== -9) {
        logger.error('拉群失败', { errcode: response.errcode, errmsg: response.errmsg }, userId)

        // 只在 周五通知
        if (new Date().getDay() === 5) {
          // 转人工处理
          await HumanTransfer.transfer(
            getChatId(userId),
            userId,
            HumanTransferType.FailedToJoinGroup,
            'onlyNotify',
            `错误码: ${response.errcode}, 错误信息: ${response.errmsg || '未知错误'}`
          )
        }
      }
    }
  } catch (e) {
    logger.error('拉群失败', e, userId)
    if (e instanceof Error) {
      // 转人工处理
      await  HumanTransfer.transfer(getChatId(userId), userId, HumanTransferType.FailedToJoinGroup, 'onlyNotify', e.message)
    }
  }
}