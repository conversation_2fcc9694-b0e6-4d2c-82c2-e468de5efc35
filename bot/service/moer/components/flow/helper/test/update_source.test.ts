import { DataService } from '../../../../getter/getData'
import { ChatStateStore, ChatStatStoreManager } from '../../../../storage/chat_state_store'
import { MoerAPI } from '../../../../../../model/moer_api/moer'
import { Config } from '../../../../../../config/config'
import { getUserId } from '../../../../../../config/chat_id'
import { loadConfigByWxId } from '../../../../../../../test/tools/load_config'


describe('updateSourceTest', () => {

  it('updateSource', async () => {
    //重刷客户来源
    const users = await DataService.getChatsByCourseNo(80)

    for (const user of users) {
      Config.setting.wechatConfig = await loadConfigByWxId(user.wx_id)
      await ChatStatStoreManager.initState(user.id)
      const chatState = ChatStateStore.get(user.id)
      if (!chatState.userSlots.source) {
        if (chatState.userSlots.phoneNumber != null) {
          const moerUser = await MoerAPI.getUserByPhone(chatState.userSlots.phoneNumber)
          const source = moerUser.source
          await DataService.updateTags(getUserId(user.id), source)
        }
      }
    }

  }, 9e8)
})