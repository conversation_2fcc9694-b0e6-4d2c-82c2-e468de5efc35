import { ChatHistoryService, IDBBaseMessage } from '../../chat_history/chat_history'
import { IWorkflowState } from '../flow'
import { LLMNode } from '../nodes/llm'
import { StringHelper } from '../../../../../lib/string'
import { LLM } from '../../../../../lib/ai/llm/LLM'
import logger from '../../../../../model/logger/logger'
import { SalesPrompt } from '../../../prompt/moer/salesPrompt'
import { ImageSearchResult, MoreImageGeneral } from '../../rag/moer_image_general'
import { sleep } from '../../../../../lib/schedule/schedule'
import { MessageSender } from '../../message/message_send'
import { IWecomMsgType } from '../../../../../lib/juzi/type'
import { MoerRag } from '../../rag/moer_embedding_search'
import { Moer<PERSON>eneralRAG } from '../../rag/moer_general'
import { EventTracker, IEventType } from '../../../../../model/logger/data_driven'


export class SalesNodeHelper {
  public static async getChatHistory(chat_id: string, rounds: number, maxLength: number = 10) {
    const chatHistory = await ChatHistoryService.getRecentConversations(chat_id, rounds, 'user')
    let cleanChatHistory = SalesNodeHelper.filterChatHistory(chatHistory)
    if (cleanChatHistory.length > maxLength) {
      cleanChatHistory = cleanChatHistory.slice(-maxLength)
    }
    for (let i = 0; i < cleanChatHistory.length; i++) {
      if (cleanChatHistory[i].content.length > 600) {
        cleanChatHistory[i].content = cleanChatHistory[i].short_description || '[营销信息]'
      }
    }
    return ChatHistoryService.formatHistoryHelper(cleanChatHistory).replace(/\n+/g, '\n')
  }

  private static filterChatHistory(chatHistory: IDBBaseMessage[]) {

    return chatHistory.filter((message) => {
      const nextMessage = chatHistory[chatHistory.indexOf(message) + 1]
      if (nextMessage && nextMessage.role === 'user') {
        return true
      }
      return !message.short_description
    })
  }

  public static async isRepeatedMsg(chat_id: string, message: string, maxMessageLength: number = 10) {
    const chatHistory = await ChatHistoryService.formatHistoryOnRole(chat_id, 'assistant', maxMessageLength)

    // 分句检查是否重复
    // Split the sentence into clauses based on punctuation marks like period, comma, semicolon, etc.
    const clauses = message.split(/[,;!?，。；！？]/).map((clause) => clause.trim()).filter((clause) => clause.length >= 5)

    const noCheckCommonClaude = ['提升专注力']

    // Check if each clause is contained within the search string
    return clauses.some((clause) => chatHistory.includes(clause) && !noCheckCommonClaude.includes(clause)) || (chatHistory.includes(message) && message.length >= 7)
  }

  public static async replyMessage(state: IWorkflowState, casePrompt: string, options?: {useRag?: boolean; recallMemory?: boolean}) {
    await LLMNode.invoke({
      state,
      customPrompt: SalesPrompt.getSalesPrompt(),
      talkStrategyPrompt: casePrompt,
      retrievedKnowledge: options?.useRag,
      customerMemory: options?.recallMemory,
      promptName: 'sales_invitation'
    })
  }

  public static async getRagContext(userMessage: string, chatId: string, round_id: string): Promise<string> {
    let ragContext = ''
    try {
      const isRag = await MoerRag.isRagEmbeddingSearch(userMessage)

      if (isRag.length > 0) {
        const ragInfo = await MoerGeneralRAG.search(userMessage, chatId, round_id)
        if (ragInfo) {
          if (ragInfo.includes('[') || ragInfo.includes(']')) {
            ragContext = `下面答案中如果提到文件资源为 [文件名称] 格式，请严格按照原始文件名称输出，不要添加额外的文件或信息，并且只参考补充信息中提到的文件资源。
## 补充信息                         
${ragInfo}`
          } else {
            ragContext = `## 补充信息\n${ragInfo}`
          }
        }
        logger.trace({ chat_id: chatId }, 'ragContext:', ragContext)
      }
    } catch (e) {
      logger.error('RAG 查询失败', e)
    }

    return ragContext
  }


  public static async sendCaseImage(chat_id: string, user_id: string, round_id: string) {

    const imageStr = await SalesPrompt.getSalesCase(chat_id, round_id)
    if (!imageStr) {
      return
    }
    EventTracker.track(chat_id, IEventType.SalesCase, { round_id: round_id, case: imageStr })

    // 最多发送2张图片
    const images = imageStr.split('，').slice(0, 2)

    const imageSearchResult = await Promise.all(images.map(async (image) => {
      const resultList = await MoreImageGeneral.searchSalesCaseImage(image, ['sales_case'], 1)
      if (resultList.length === 0) {
        return {
          description: '',
          url: ''
        } as ImageSearchResult
      }
      return resultList[0]
    }))

    let isSendCaseImage = false
    for (const image of imageSearchResult) {

      if (await SalesNodeHelper.isRepeatedMsg(chat_id, image.description, 20) || StringHelper.isEmpty(image.url))
      {
        continue
      }

      isSendCaseImage = true
      await sleep(2000)
      await MessageSender.sendById({
        user_id: user_id,
        chat_id: chat_id,
        ai_msg: image.description,
        send_msg:{
          type: IWecomMsgType.Image,
          url: image.url
        }
      })
    }

    if (isSendCaseImage) {
      const chatHistory = await SalesNodeHelper.getChatHistory(chat_id, 5)
      const res = await LLM.predict(`参考图片信息与聊天上下文
## 案例图片信息
${imageSearchResult.map((image) => image.description).join('\n')}

## 对话历史
${chatHistory}

背景信息
当前客户已经完成了5天冥想入门体验营，你正在给客户发送案例图片，推荐21天课程
任务
根据图片信息，输出一句话引导客户查看案例图片

示例
给您看看我们过往学员，参加系统班的感悟分享
`, { model: 'gpt-5-mini', meta: { name: 'send_case_image', chat_id: chat_id, round_id: round_id } })
      await MessageSender.sendById({
        chat_id: chat_id,
        user_id: user_id,
        ai_msg: res
      })
    }
  }
}