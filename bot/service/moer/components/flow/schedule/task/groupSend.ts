import { ISendMedia, ITask } from '../../../schedule/type'
import { TaskName } from '../type'
import { BaseTask, getState, trackProcess } from './baseTask'
import { getScript, ISendWrapper } from '../../../script/script'
import { Config } from '../../../../../../config/config'
import { ChatStateStore, ChatStatStoreManager } from '../../../../storage/chat_state_store'
import { MoerNode } from '../../nodes/type'
import { JuziAPI } from '../../../../../../lib/juzi/api'
import { DataService } from '../../../../getter/getData'
import { IScheduleTime, isScheduleTimeAfter } from '../../../schedule/creat_schedule_task'
import logger from '../../../../../../model/logger/logger'
import { CourseFeedBackDay3InClass } from '../../nodes/courseFeedback'
import { sleep } from '../../../../../../lib/schedule/schedule'
import { inviteToGroup } from '../../helper/inviteToGroup'
import { ContextBuilder } from '../../../agent/context'
import { LLM } from '../../../../../../lib/ai/llm/LLM'
import { MessageSender } from '../../../message/message_send'
import removeMarkdown from 'markdown-to-text'
import { ChatHistoryService } from '../../../chat_history/chat_history'
import { FreeTalk } from '../../../agent/freetalk'
import { MoerAPI } from '../../../../../../model/moer_api/moer'
import { IWecomMsgType } from '../../../../../../lib/juzi/type'
import { conditionJudgeMap } from '../../../visualized_sop/visualized_sop_variable'

function scheduleTimeToString(schedule: IScheduleTime): string {
  // 星期映射
  const weekdays: { [key: number]: string } = {
    1: '周一',
    2: '周二',
    3: '周三',
    4: '周四',
    5: '周五',
    6: '周六',
    7: '周日',
  }

  // 判断 is_course_week 并赋值相应的字符串
  let weekType: string
  if (schedule.is_course_week) {
    weekType = '开课周'
  } else {
    weekType = '进量周'
  }

  // 获取星期字符串，如果 day 不在 1-7 范围内，设置为 "未知星期"
  const dayString = weekdays[schedule.day] || '未知星期'

  // 组合最终字符串
  return `${weekType} ${dayString} ${schedule.time}`
}

/**
 * 定时群发任务
 */
export class GroupSend extends BaseTask {
  public static async getTask(chat_id: string, user_id: string) {
    const tasks: ITask[] = []
    const baseInfo = {
      chatId: chat_id,
      userId: user_id,
    }

    tasks.push({
      ...baseInfo,
      name: TaskName.PreCourseFinishInteraction,
      scheduleTime: {
        is_course_week: false,
        day: 2,
        time: '21:20:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.PreCourseFinishInteraction,
      scheduleTime: {
        is_course_week: false,
        day: 3,
        time: '21:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.PreCourseFinishInteraction,
      scheduleTime: {
        is_course_week: false,
        day: 4,
        time: '21:10:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.PreCourseFinishInteraction,
      scheduleTime: {
        is_course_week: false,
        day: 5,
        time: '21:15:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.PreCourseFinishInteraction,
      scheduleTime: {
        is_course_week: false,
        day: 6,
        time: '21:30:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.GroupSendGroupInvited,
      scheduleTime: {
        is_course_week: false,
        day: 5,
        time: '20:40:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.Tuesday1403,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '14:03:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.Wednesday1435,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '14:35:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.GroupSendPushGroupInvited,
      scheduleTime: {
        is_course_week: false,
        day: 5,
        time: '22:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.GroupSendTeacherIntroduction,
      scheduleTime: {
        is_course_week: false,
        day: 6,
        time: '12:23:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.GroupSendPushEnergyTest,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '16:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.PushOpeningCeremony,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '19:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.PushOpeningCeremony,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '20:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.EndOpeningCeremony,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '20:40:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.PushOpeningCeremony,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '21:13:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.TPlusNTask,
      scheduleTime: {
        is_course_week: false,
        day: 7,
        time: '21:13:00'
      },
      tag:'t+1_18:50'
    })

    // 开课周任务
    tasks.push({
      ...baseInfo,
      name: TaskName.MorningGreeting,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '08:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.GroupSendPushEnergyTest,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '12:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ShareChange,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '15:35:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.FirstDayPhoneLoginReminder,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '17:50:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.FirstDayClassReminder1,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '19:00:00'
      }
    })

    // tasks.push({
    //   ...baseInfo,
    //   name: TaskName.FirstDayClassReminder2,
    //   scheduleTime: {
    //     is_course_week: true,
    //     day: 1,
    //     time: '19:50:00'
    //   }
    // })

    tasks.push({
      ...baseInfo,
      name: TaskName.ClassStartNotification,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '19:55:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '20:05:00'
      },
      tag: '1st_day_check1'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '20:10:00'
      },
      tag: '1st_day_check20_10'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '20:21:00'
      },
      tag: '1st_day_check2'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '20:40:00'
      },
      tag: '1st_day_check3'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '21:28:00'
      },
      tag: '1st_day_check4'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.PostClassFeedback,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '21:45:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.GroupSendPushGroupInvited,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '21:50:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.SendRecording,
      scheduleTime: {
        is_course_week: true,
        day: 1,
        time: '22:15:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.RemindRecording,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '07:10:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CourseCompleteAwardAndFeedback,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '12:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.SecondDayVideoChannel,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '16:10:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.UnPaidRemind1,
      scheduleTime: {
        post_course_week:1,
        day: 2,
        time: '17:30:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.SecondDayClassReminder,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '18:05:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.SecondDayClassReminder1,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '19:00:00'
      }
    })

    // tasks.push({
    //   ...baseInfo,
    //   name: TaskName.SecondDayClassReminder2,
    //   scheduleTime: {
    //     is_course_week: true,
    //     day: 2,
    //     time: '19:55:00'
    //   }
    // })

    tasks.push({
      ...baseInfo,
      name: TaskName.ClassStartNotification,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '20:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.UnPaidRemind2,
      scheduleTime: {
        post_course_week:1,
        day: 2,
        time: '20:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '20:05:00'
      },
      tag: '2nd_day_check1'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '20:10:00'
      },
      tag: '2nd_day_check2'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '20:25:00'
      },
      tag: '2nd_day_check3'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '20:34:00'
      },
      tag: '2nd_day_check4'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '20:45:00'
      },
      tag: '2nd_day_check20_45'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.SecondClassEndFeedback,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '21:15:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.SendRecording,
      scheduleTime: {
        is_course_week: true,
        day: 2,
        time: '22:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.RemindRecording,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '06:30:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CourseCompleteAwardAndFeedback,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '11:53:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.GroupSendPushGroupInvited,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '12:30:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.RedShoesSharing,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '16:20:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirdDayClassReminder,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '18:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirdDayClassReminder1,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '19:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirdDayClassReminder2,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '19:50:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ClassStartNotification,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '20:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '20:05:00'
      },
      tag: '3rd_day_check20_05'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '20:10:00'
      },
      tag: '3rd_day_check1'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '20:25:00'
      },
      tag: '3rd_day_check2'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '20:34:00'
      },
      tag: '3rd_day_check20_34'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '21:08:00'
      },
      tag: '3rd_day_check3'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '21:30:00'
      },
      tag: '3rd_day_check4'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.SalesPitch,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '22:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.FunctionEmphasis,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '22:40:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.SendRecording,
      scheduleTime: {
        is_course_week: true,
        day: 3,
        time: '22:35:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.MorningGreeting,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '07:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ExtraClassResearch,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '09:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ExtraClassNotification,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '14:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CourseCompleteAwardAndFeedback,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '15:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.LayingOutSummaryYesterday,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '16:35:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ScheduleExtraClass,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '18:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CountdownToClass,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '19:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ThirdClassReminder,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '19:50:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.ClassStartNotification,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '19:58:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '20:05:00'
      },
      tag: '4th_day_check1'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '20:10:00'
      },
      tag: '4th_day_check20_10'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '20:25:00'
      },
      tag: '4th_day_check2'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '20:45:00'
      },
      tag: '4th_day_check3'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '21:15:00'
      },
      tag: '4th_day_check4'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '21:30:00'
      },
      tag: '4th_day_check5'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CheckIsInClass,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '21:45:00'
      },
      tag: '4th_day_check21_45'
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.FinalMarketingPitch,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '22:25:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.PaymentAbilityReminder,
      scheduleTime: {
        is_course_week: true,
        day: 4,
        time: '23:30:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.SendRecording,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '00:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.AdditionalMarketing,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '01:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.MorningGreeting,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '07:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CompletionReward,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '15:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CampEndReminder,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '16:20:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CampEndReminder,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '17:30:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CampEndReminder,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '20:20:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CampEndReminder,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '21:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CampEndReminder,
      scheduleTime: {
        is_course_week: true,
        day: 5,
        time: '21:30:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CampEndReminder,
      scheduleTime: {
        is_course_week: true,
        day: 6,
        time: '10:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CampEndReminder,
      scheduleTime: {
        is_course_week: true,
        day: 6,
        time: '12:01:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CampEndReminder,
      scheduleTime: {
        is_course_week: true,
        day: 6,
        time: '18:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CampEndReminder,
      scheduleTime: {
        is_course_week: true,
        day: 6,
        time: '20:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CampEndReminder,
      scheduleTime: {
        is_course_week: true,
        day: 6,
        time: '22:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CampEndReminder,
      scheduleTime: {
        is_course_week: true,
        day: 7,
        time: '10:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CampEndReminder,
      scheduleTime: {
        is_course_week: true,
        day: 7,
        time: '14:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CampEndReminder,
      scheduleTime: {
        is_course_week: true,
        day: 7,
        time: '17:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CampEndReminder,
      scheduleTime: {
        is_course_week: true,
        day: 7,
        time: '19:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CampEndReminder,
      scheduleTime: {
        is_course_week: true,
        day: 7,
        time: '21:00:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CampEndReminder,
      scheduleTime: {
        post_course_week: 1,
        day: 1,
        time: '11:30:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CampEndReminder,
      scheduleTime: {
        post_course_week: 1,
        day: 1,
        time: '17:20:00'
      }
    })

    tasks.push({
      ...baseInfo,
      name: TaskName.CampEndReminder,
      scheduleTime: {
        post_course_week: 1,
        day: 1,
        time: '21:20:00'
      }
    })

    return tasks
  }

  @trackProcess
  public async process(task: ITask) {
    if (!task.scheduleTime) {
      return
    }

    const taskTime = task.scheduleTime as IScheduleTime

    // 销售期不进行 SOP 突然插入
    if (isScheduleTimeAfter(taskTime, {
      is_course_week:true,
      day: 4,
      time: '22:10:00'
    }))  {
      if (await ChatHistoryService.isLastMessageWithDuration(task.chatId, 10, 'minute')) {
        return
      }
    }

    if (!(await conditionJudgeMap['腾讯投放3元客户']({ chatId:task.chatId, userId:task.userId }))) {
      logger.log(`chat_id: ${task.chatId} 不是腾讯3元，所以不执行老sop ${task.name} ${JSON.stringify(task)}`)
      return
    }

    const scripts = getScript().course_day

    switch (task.name) {
      case TaskName.GroupSendTeacherIntroduction:
        await this.sendMsg(task.userId, task.chatId, [scripts.teacher_introduction_1, scripts.teacher_introduction_2.content, scripts.teacher_introduction_image, scripts.teacher_introduction_image_2], scripts.teacher_introduction_1.description)
        break

      case TaskName.GroupSendGroupInvited:
        await inviteToGroup(task.userId)
        await this.sendMsg(task.userId, task.chatId, [scripts.group_invited.content], scripts.group_invited.description)
        break

      case TaskName.Tuesday1403:
      { const isCompleteThisClass2 = await DataService.isCompletedCourse(task.chatId, { day:1, is_recording:false })
        const isCompleteThisClassRecording2 = await DataService.isCompletedCourse(task.chatId, { day:1, is_recording:true })
        if (isCompleteThisClass2 || isCompleteThisClassRecording2) break
        await this.sendMsg(task.userId, task.chatId, [scripts.tuesday_1403_1, await scripts.tuesday_1403_2.content(task.chatId)], scripts.tuesday_1403_1.description)
        break }

      case TaskName.Wednesday1435:
      { const isCompleteThisClass3 = await DataService.isCompletedCourse(task.chatId, { day:2, is_recording:false })
        const isCompleteThisClassRecording3 = await DataService.isCompletedCourse(task.chatId, { day:2, is_recording:true })
        if (isCompleteThisClass3 || isCompleteThisClassRecording3) break
        await this.sendMsg(task.userId, task.chatId, [scripts.wednesday_1435.content], scripts.wednesday_1435.description)
        break }

      case TaskName.GroupSendPushGroupInvited:
        // 检查客户是否在默认群内（用于拉群等功能）
        if (await JuziAPI.isInGroup({
          imBotId: Config.setting.wechatConfig?.id as string,
          imRoomId: Config.setting.wechatConfig?.classGroupId as string,
          imContactId: task.userId
        })) { // 在群内不再通知进群
          break
        }

        if (!taskTime.is_course_week) {
          if (taskTime.day === 5) {
            await this.sendMsg(task.userId, task.chatId, [scripts.push_group_invited_1.content], scripts.push_group_invited_1.description)
          } else if (taskTime.day === 7) {
            if (taskTime.time === '17:00:00') {
              await this.sendMsg(task.userId, task.chatId, scripts.push_group_invited_2.content, scripts.push_group_invited_2.description)
            }
          }
        } else {
          if (taskTime.day === 1) {
            await this.sendMsg(task.userId, task.chatId, scripts.push_group_invited_4.content, scripts.push_group_invited_4.description)
          } else if (taskTime.day === 3) {
            await this.sendMsg(task.userId, task.chatId, scripts.push_group_invited_5.content, scripts.push_group_invited_5.description)
          }
        }

        await inviteToGroup(task.userId)
        break

      case TaskName.GroupSendPushEnergyTest:
        if (await DataService.getEnergyTestScore(task.chatId) !== null) {
          return
        }
        if (!taskTime.is_course_week && taskTime.day === 7) {
          const isInGroup = await DataService.isInGroup(task.userId)
          const isCompletePreCourse = await DataService.isCompletedCourse(task.chatId, { day: 0 })
          const isCompleteEnergyTest = await DataService.getEnergyTestScore(task.chatId) !== null
          if (!isInGroup && !isCompletePreCourse && !isCompleteEnergyTest) {
            await inviteToGroup(task.userId)
            await this.sendMsg(task.userId, task.chatId, await scripts.task_check_not_finish_all.content(task.chatId), scripts.task_check_not_finish_all.description)
          } else if (!isCompletePreCourse) {
            await this.sendMsg(task.userId, task.chatId, await scripts.task_check_pre_course_not_finish.content(task.chatId), scripts.task_check_pre_course_not_finish.description)
          } else if (!isCompleteEnergyTest) {
            await this.sendMsg(task.userId, task.chatId, scripts.task_check_energy_test_not_finish.content, scripts.task_check_energy_test_not_finish.description)
          } else if (!isInGroup) {
            await this.sendMsg(task.userId, task.chatId, scripts.task_check_not_in_group.content, scripts.task_check_not_in_group.description)
            await inviteToGroup(task.userId)
          }
        } else if (taskTime.is_course_week && taskTime.day === 1) {
          await this.sendMsg(task.userId, task.chatId, scripts.push_energy_test_2.content, scripts.push_energy_test_2.description)
        }
        break

      case TaskName.PreCourseFinishInteraction:
      { const isCompletePreCourse = await DataService.isCompletedCourse(task.chatId, { day: 0 })
        if (!isCompletePreCourse) {
          return
        }
        if (taskTime.day === 2) {
          await this.sendMsg(task.userId, task.chatId, scripts.pre_course_complete_greeting_day_2_21_20.content, scripts.pre_course_complete_greeting_day_2_21_20.description)
        } else if (taskTime.day === 3) {
          await this.sendMsg(task.userId, task.chatId, scripts.pre_course_complete_greeting_day_3_21_00.content, scripts.pre_course_complete_greeting_day_3_21_00.description)
        } else if (taskTime.day === 4) {
          await this.sendMsg(task.userId, task.chatId, scripts.pre_course_complete_greeting_day_4_21_10.content, scripts.pre_course_complete_greeting_day_4_21_10.description)
        } else if (taskTime.day === 5) {
          await this.sendMsg(task.userId, task.chatId, scripts.pre_course_complete_greeting_day_5_21_15.content, scripts.pre_course_complete_greeting_day_5_21_15.description)
        } else if (taskTime.day === 6) {
          await this.sendMsg(task.userId, task.chatId, scripts.pre_course_complete_greeting_day_6_21_30.content, scripts.pre_course_complete_greeting_day_6_21_30.description)
        }
        break }

      case TaskName.TPlusNTask:
        if (task.tag === 't_plus_1_18_50' && ChatStateStore.getNodeCount(task.chatId, FreeTalk.name) < 30) {
          await this.sendMsg(task.userId, task.chatId, scripts.pre_course_complete_interaction_T_plus_1.content, scripts.pre_course_complete_interaction_T_plus_1.description)
        }
        break

      case TaskName.PushOpeningCeremony:
        if (taskTime.time === '19:00:00') {
          await this.sendMsg(task.userId, task.chatId, [scripts.opening_ceremony_1_1.content, scripts.opening_ceremony_1_2.content, scripts.opening_ceremony_1_3.content, scripts.opening_ceremony_1_4.content,], scripts.opening_ceremony_1_1.description)
        } else if (taskTime.time === '20:00:00') {
          await this.sendMsg(task.userId, task.chatId, [scripts.opening_ceremony_3.content], scripts.opening_ceremony_3.description)
        } else if (taskTime.time === '21:13:00') {
          await this.sendMsg(task.userId, task.chatId, [scripts.opening_ceremony_4_voice, scripts.opening_ceremony_4_text.content], scripts.opening_ceremony_4.description)
        }
        break

      case TaskName.EndOpeningCeremony:
      { const notification = await scripts.ceremony_end_1.content(task.chatId)
        await this.sendMsg(task.userId, task.chatId, [notification, scripts.ceremony_end_2.content, scripts.ceremony_end_2_file], scripts.ceremony_end_1.description)
        break }

      case TaskName.FirstDayPhoneLoginReminder:
        await this.sendMsg(task.userId, task.chatId, [await scripts.first_day_phone_login_reminder1.content(task.chatId, task.userId), scripts.first_day_phone_login_reminder2.content], scripts.first_day_phone_login_reminder1.description)
        break

      case TaskName.UnPaidRemind1: {
        const isPaid = await DataService.isPaidSystemCourse(task.chatId)
        //付款则不操作
        if (isPaid) {
          break
        }
        await this.sendMsg(task.userId, task.chatId, [await scripts.unpaid_remind_1_1.content(), scripts.unpaid_remind_1_2, scripts.unpaid_remind_1_3.content])
        break
      }

      case TaskName.UnPaidRemind2: {
        const today = new Date()
        if (
          today.getFullYear() === 2025 &&
            today.getMonth() === 5 && // 月份从 0 开始，6 月是 5
            today.getDate() === 24
        ) {
          return
        }

        const isPaid = await DataService.isPaidSystemCourse(task.chatId)
        //付款则不操作
        if (isPaid) {
          break
        }

        await this.sendMsg(task.userId, task.chatId, [await scripts.unpaid_remind_2_1.content()])
        break
      }

      case TaskName.UnPaidRemind3: {
        const isPaid = await DataService.isPaidSystemCourse(task.chatId)
        //付款则不操作
        if (isPaid) {
          break
        }

        const link = (await MoerAPI.get188CourseInfo()).shortLink
        await this.sendMsg(task.userId, task.chatId, `晚上8点  红靴子财富版  马上开始啦！提前进直播间哦：${link}`)
        break
      }

      case TaskName.MorningGreeting:
        if (!taskTime.is_course_week) {
          return
        }
        if (taskTime.day === 1) {
          await this.sendMsg(task.userId, task.chatId, scripts.morning_greeting_day1.content, scripts.morning_greeting_day1.description)
          // 检查客户是否在默认群内（用于拉群等功能）
          const isInGroup = await JuziAPI.isInGroup({
            imBotId: Config.setting.wechatConfig?.id as string,
            imRoomId: Config.setting.wechatConfig?.classGroupId as string,
            imContactId: task.userId
          })

          if (!isInGroup) {
            await this.sendMsg(task.userId, task.chatId, '同学没加群的话，可以进一下学习群，晚上学习课程动态班班都会发在群里哈', '催进群')
            await inviteToGroup(task.userId)
          }
        } else if (taskTime.day === 4) {
          await this.sendMsg(task.userId, task.chatId, await scripts.morning_greeting_day4.content(task.chatId), scripts.morning_greeting_day4.description)
        } else if (taskTime.day === 5 && await DataService.isPaidSystemCourse(task.chatId)) {
          await this.sendMsg(task.userId, task.chatId, await scripts.morning_greeting_day5_has_purchase_system_course_07_00.content(task.chatId), scripts.morning_greeting_day5_has_purchase_system_course_07_00.description)
        } else if (taskTime.day === 5 && !await DataService.isPaidSystemCourse(task.chatId)) {
          const isCompletedAllCourse = await DataService.isCompletedAllCourse(task.chatId)
          if (isCompletedAllCourse) {
            await this.sendMsg(task.userId, task.chatId, await scripts.morning_greeting_day5_1.content(task.chatId), scripts.morning_greeting_day5_1.description)
            break
          }
          await this.sendMsg(task.userId, task.chatId, await scripts.morning_greeting_day5_1_1.content(task.chatId), scripts.morning_greeting_day5_1_1.description)
        }
        break

      case TaskName.ShareChange:
      { const userSlot = await ContextBuilder.getCustomerPortrait(task.chatId)
        if (userSlot.includes('情绪减压') || userSlot.includes('专注提升') || userSlot.includes('睡眠改善') || userSlot.includes('财富能量') || userSlot.includes('亲密关系') || userSlot.includes('灵性成长')) {
          return
        }
        ChatStateStore.update(task.chatId, {
          nextStage: MoerNode.IntentionQueryBeforeClass
        })
        await this.sendMsg(task.userId, task.chatId, [scripts.ask_before_class.content, scripts.share_change.content, scripts.share_change_link], scripts.share_change.description)
        break }

      case TaskName.FirstDayClassReminder1:
      { if (await DataService.isInClass(task.chatId, { day: 1 })) {
        break
      }

      await this.sendMsg(task.userId, task.chatId, [await scripts.first_day_class_reminder1.content(task.chatId), scripts.first_day_class_reminder1_1, scripts.first_day_class_reminder1_2, scripts.first_day_class_reminder1_3.content], scripts.first_day_class_reminder1.description)
      // 检查客户是否在默认群内（用于拉群等功能）
      const isInGroup = await JuziAPI.isInGroup({
        imBotId: Config.setting.wechatConfig?.id as string,
        imRoomId: Config.setting.wechatConfig?.classGroupId as string,
        imContactId: task.userId
      })

      if (!isInGroup) {
        await this.sendMsg(task.userId, task.chatId, [scripts.first_day_class_push_group], scripts.first_day_class_push_group.description)
      }

      break }

      case TaskName.FirstDayClassReminder2:
        await this.sendMsg(task.userId, task.chatId, [scripts.first_day_class_reminder2.content, scripts.first_day_class_reminder2_image, scripts.first_day_class_reminder3.content],
          scripts.first_day_class_reminder2.description)
        // TODO 提醒图
        break

      case TaskName.ClassStartNotification:
        if (!taskTime.is_course_week) {
          return
        }
        switch (taskTime.day) {
          case 1:
          { const liveLink_first = await DataService.getCourseLink(1, task.chatId)
            await this.sendMsg(task.userId, task.chatId, await scripts.first_day_class_start.content(task.userId), scripts.first_day_class_start.description)
            await this.sendMsg(task.userId, task.chatId, scripts.first_day_class_start_link(liveLink_first), scripts.first_day_class_start.description)
            break }
          case 2:
          { const liveLink_second = await DataService.getCourseLink(2, task.chatId)
            await this.sendMsg(task.userId, task.chatId, await scripts.second_day_class_start.content(task.userId), scripts.second_day_class_start.description)
            await this.sendMsg(task.userId, task.chatId, scripts.second_day_class_start_link(liveLink_second), scripts.second_day_class_start.description)
            break }
          case 3:
          { const liveLink_third = await DataService.getCourseLink(3, task.chatId)
            await this.sendMsg(task.userId, task.chatId, scripts.third_day_start_notification_link(liveLink_third), scripts.third_day_start_notification.description)
            break }
          case 4:
            await this.sendMsg(task.userId, task.chatId, await scripts.fourth_class_start.content(task.chatId), scripts.fourth_class_start.description)
            break
          default:
            break
        }

        break

      case TaskName.CheckIsInClass:
        if (!taskTime.is_course_week) {
          break
        }

        if (taskTime.day === 1) {
          if (await DataService.isInClass(task.chatId, { day: 1 })) {
            break
          }

          if (task.tag === '1st_day_check1') {
            await this.sendMsg(task.userId, task.chatId, [scripts.first_day_not_in_class_1, await scripts.first_day_not_in_class_1_2.content(task.chatId)], scripts.first_day_not_in_class_1.description)
          } else if (task.tag === '1st_day_check20_10') {
            //todo 改成语音
            await this.sendMsg(task.userId, task.chatId, [scripts.first_day_not_in_class_20_10.content, await scripts.first_day_not_in_class_20_10_link.content(task.chatId)], scripts.first_day_not_in_class_20_10.description)
          } else if (task.tag === '1st_day_check2') {
            await this.sendMsg(task.userId, task.chatId, [await scripts.first_day_not_in_class_2.content(task.chatId), scripts.first_day_not_in_class_2_image], scripts.first_day_not_in_class_2.description)
          } else if (task.tag === '1st_day_check3') {
            await this.sendMsg(task.userId, task.chatId, await scripts.first_day_not_in_class_3.content(task.chatId), scripts.first_day_not_in_class_3.description)
          } else if (task.tag === '1st_day_check4') {
            await this.sendMsg(task.userId, task.chatId, await scripts.first_day_not_in_class_4.content(task.chatId), scripts.first_day_not_in_class_4.description)
          }

        } else if (taskTime.day === 2) {
          if (await DataService.isInClass(task.chatId, { day: 2 })) {
            break
          }

          if (task.tag === '2nd_day_check1') { // 20:05
            await this.sendMsg(task.userId, task.chatId, [scripts.second_day_class_no_in_1, await scripts.second_day_class_no_in_1_link.content(task.chatId)], scripts.second_day_class_no_in_1.description)
          } else if (task.tag === '2nd_day_check2') { // 20:10
            await this.sendMsg(task.userId, task.chatId, [scripts.second_day_class_no_in_2_image, await scripts.second_day_class_no_in_2.content(task.chatId)], scripts.second_day_class_no_in_2.description)
          } else if (task.tag === '2nd_day_check3') { // 20:25
            await this.sendMsg(task.userId, task.chatId, [await scripts.second_day_class_no_in_3.content(task.chatId), scripts.second_day_class_no_in_3_image], scripts.second_day_class_no_in_3.description)
          } else if (task.tag === '2nd_day_check4') { // 20:34
            await this.sendMsg(task.userId, task.chatId, await scripts.second_day_class_no_in_4.content(task.chatId), scripts.second_day_class_no_in_4.description)
          } else if (task.tag === '2nd_day_check20_45') { // 20:45
            await this.sendMsg(task.userId, task.chatId, [scripts.second_day_class_no_in_20_45, await scripts.second_day_class_no_in_20_45_link.content(task.chatId)], scripts.second_day_class_no_in_20_45.description)
          }

        } else if (taskTime.day === 3) {
          if (await DataService.isInClass(task.chatId, { day: 3 })) {
            break
          }

          if (task.tag === '3rd_day_check20_05') { // 20:05
            await this.sendMsg(task.userId, task.chatId, [scripts.third_day_class_no_in_20_05, await scripts.third_day_class_no_in_20_05_link.content(task.chatId)], scripts.third_day_class_no_in_20_05.description)
          } else if (task.tag === '3rd_day_check1') { // 20:10
            await this.sendMsg(task.userId, task.chatId, await scripts.third_day_class_no_in_1.content(task.chatId), scripts.third_day_class_no_in_1.description)
          } else if (task.tag === '3rd_day_check2') { // 20:25
            await this.sendMsg(task.userId, task.chatId, await scripts.third_day_class_no_in_2.content(task.chatId), scripts.third_day_class_no_in_2.description)
          } else if (task.tag === '3rd_day_check20_34') { // 20:34
            await this.sendMsg(task.userId, task.chatId, [scripts.third_day_class_no_in_20_34, await scripts.third_day_class_no_in_20_34_link.content(task.chatId)], scripts.third_day_class_no_in_20_34.description)
          } else if (task.tag === '3rd_day_check3') { // 21:08
            await this.sendMsg(task.userId, task.chatId, await scripts.third_day_class_no_in_3.content(task.chatId), scripts.third_day_class_no_in_3.description)
          } else if (task.tag === '3rd_day_check4') { // 21:30
            await this.sendMsg(task.userId, task.chatId, await scripts.third_day_class_no_in_4.content(task.chatId), scripts.third_day_class_no_in_4.description)
          }
        } else if (taskTime.day === 4) {
          if (await DataService.isInClass(task.chatId, { day: 4 })) {
            break
          }

          if (task.tag === '4th_day_check1') { //20:05
            await this.sendMsg(task.userId, task.chatId, await scripts.fourth_day_class_no_in_1.content(task.chatId), scripts.fourth_day_class_no_in_1.description)
          } else if (task.tag === '4th_day_check20_10') { //20:10
            await this.sendMsg(task.userId, task.chatId, [scripts.fourth_day_class_no_in_20_10, await scripts.fourth_day_class_no_in_20_10_link.content(task.chatId)], scripts.fourth_day_class_no_in_20_10.description)
          } else if (task.tag === '4th_day_check2') { //20:25
            await this.sendMsg(task.userId, task.chatId, await scripts.fourth_day_class_no_in_2.content(task.chatId), scripts.fourth_day_class_no_in_2.description)
          } else if (task.tag === '4th_day_check3') { //20:45
            await this.sendMsg(task.userId, task.chatId, await scripts.fourth_day_class_no_in_3.content(task.chatId), scripts.fourth_day_class_no_in_3.description)
          } else if (task.tag === '4th_day_check4') { //21:15
            await this.sendMsg(task.userId, task.chatId, [await scripts.fourth_day_class_no_in_4.content(task.chatId), scripts.fourth_day_class_no_in_4_image], scripts.third_day_class_no_in_4.description)
          } else if (task.tag === '4th_day_check5') { //21:30
            await this.sendMsg(task.userId, task.chatId, scripts.fourth_day_class_no_in_5.content, scripts.fourth_day_class_no_in_5.description)
          } else if (task.tag === '4th_day_check21_45') { //21:45
            await this.sendMsg(task.userId, task.chatId, await scripts.fourth_day_class_no_in_21_45.content(task.chatId), scripts.fourth_day_class_no_in_21_45.description)
          }
        }
        break

      case TaskName.PostClassFeedback:
      { ChatStateStore.update(task.chatId, {
        nextStage: MoerNode.CourseFeedBackDay1,
      })
      // 发送打卡模版
      await this.sendMsg(task.userId, task.chatId, scripts.day1_homework_template.content, scripts.day1_homework_template.description)

      const isInClass = await DataService.isInClass(task.chatId, { day: 1 })
      const isCompletedCourse = await DataService.isCompletedCourse(task.chatId, { day: 1 })

      // 未到课
      if (!isInClass) {
        const userSlots = await ContextBuilder.getCustomerPortrait(task.chatId)
        if (userSlots) {
          const llmOutput = await LLM.predict(`参考之前客户和我们沟通提取的信息，特别是痛点

## 客户画像
${userSlots}

今天是第一天客户没有来上课，结合客户的痛点和本堂课对其的个性化价值，提醒其注意回放补课。
第一节课的内容有：周一晚上的情绪减压课，唐宁老师会重点讲冥想是如何帮我们处理情绪背后的问题，课程强调了能量频率对生活质量的重要影响，并通过调身、调息、调心三个维度教授冥想的具体技巧。是开始基础课，包括了"释放压力冥想练习" 和 "沉浸式秒睡"两个练习。
例如："同学，在课堂上没看到你诶，很可惜。今天老师详细给我们讲了【xx】，对于【xx】很有帮助呢！"

输出以 "同学，在课堂上没看到你诶，很可惜" 开头`)
          await this.sendMsg(task.userId, task.chatId, llmOutput, scripts.post_class_feedback_not_in_class.description)
        } else {
          await this.sendMsg(task.userId, task.chatId, scripts.post_class_feedback_not_in_class.content, scripts.post_class_feedback_not_in_class.description)
        }
      } else {
        if (!isCompletedCourse) {
          await this.sendMsg(task.userId, task.chatId, [scripts.post_class_feedback_in_class_1.content, scripts.post_class_feedback_in_class_2.content], scripts.post_class_feedback_in_class_1.description)
        } else {
          await this.sendMsg(task.userId, task.chatId, [scripts.first_day_class_complete.content, scripts.first_day_course_complete_reward_link, scripts.post_class_feedback_complete.content], scripts.post_class_feedback_complete.description)
          ChatStateStore.update(task.chatId, { state: { has_send_class1_reward: true } })
        }
      }
      break }

      case TaskName.SendRecording:
        if (!taskTime.is_course_week) {
          return
        }
        switch (taskTime.day) {
          case 1:
            await this.sendMsg(task.userId, task.chatId, [await scripts.send_recording_day_1.content(task.chatId)], scripts.send_recording_day_1.description)
            break
          case 2:
            if (await DataService.isCompletedCourse(task.chatId, { day: 2 })) {
              const recordingLink = await DataService.getCourseLink(2, task.chatId, true)
              await this.sendMsg(task.userId, task.chatId, scripts.send_recording_day_2_complete_course_link(recordingLink), scripts.send_recording_day_2_complete_course.description)
            } else {
              const recordingLink = await DataService.getCourseLink(2, task.chatId, true)
              await this.sendMsg(task.userId, task.chatId, scripts.send_recording_day_2_not_complete_course_link(recordingLink), scripts.send_recording_day_2_not_complete_course.description)
            }
            break
          case 3:
          { const isPaid = await DataService.isPaidSystemCourse(task.chatId)
            if (isPaid) {
              await this.sendMsg(task.userId, task.chatId, [await scripts.send_recording_day_3_complete_course_1.content(task.chatId), scripts.send_recording_day_3_complete_course_2_paid.content], scripts.send_recording_day_3_complete_course_1.description)
              break
            }

            if (await DataService.isCompletedCourse(task.chatId, { day: 3 })) {
              await this.sendMsg(task.userId, task.chatId, [await scripts.send_recording_day_3_complete_course_1.content(task.chatId), scripts.send_recording_day_3_complete_course_2.content], scripts.send_recording_day_3_complete_course_1.description)
            } else {
              await this.sendMsg(task.userId, task.chatId, [await scripts.send_recording_day_3_not_complete_course_1.content(task.chatId), scripts.send_recording_day_3_not_complete_course_2.content], scripts.send_recording_day_3_not_complete_course_1.description)
            }
            break }
          case 5:
            await this.sendMsg(task.userId, task.chatId, await scripts.send_recording_day_4_recording.content(task.chatId), scripts.send_recording_day_4_recording.description)
            break
          default:
            break
        }
        break

      case TaskName.RemindRecording:
        if (!taskTime.is_course_week) {
          return
        }
        switch (taskTime.day) {
          case 2:
            if (await DataService.isCompletedCourse(task.chatId, { day: 1 })) {
              await this.sendMsg(task.userId, task.chatId, scripts.remind_recording_day_2_complete.content, scripts.remind_recording_day_2_complete.description)
            } else {
              await this.sendMsg(task.userId, task.chatId, [await scripts.remind_recording_day_2_not_complete_1.content(task.chatId), scripts.remind_recording_day_2_not_complete_2.content], scripts.remind_recording_day_2_not_complete_1.description)
            }
            break
          case 3:
            if (await DataService.isCompletedCourse(task.chatId, { day: 2 })) {
              await this.sendMsg(task.userId, task.chatId, scripts.remind_recording_day_3_complete.content, scripts.remind_recording_day_3_complete.description)
            } else {
              await this.sendMsg(task.userId, task.chatId, [await scripts.second_day_class_not_complete.content(task.userId, task.chatId), scripts.remind_recording_day_3_not_complete_2.content], scripts.second_day_class_not_complete.description)
            }
            break
        }
        break

      case TaskName.SecondDayVideoChannel:
        if (!await DataService.isCompletedCourse(task.chatId, { day: 1 }) && !await DataService.isCompletedCourse(task.chatId, { day: 1, is_recording  : true })) {
          await this.sendMsg(task.userId, task.chatId, [await scripts.second_day_video_channel.content(task.chatId)], scripts.second_day_video_channel.description)
        }
        break

      case TaskName.SecondDayClassReminder:
        await this.sendMsg(task.userId, task.chatId, [scripts.second_day_class_reminder.content, scripts.second_day_class_reminder_voice], scripts.second_day_class_reminder.description)
        break

      case TaskName.SecondDayClassReminder1:
      { const recordingLink = await DataService.getCourseLink(2, task.chatId)
        await this.sendMsg(task.userId, task.chatId, scripts.second_day_class_reminder1_link(recordingLink), scripts.second_day_class_reminder1.description)
        break }

      case TaskName.SecondDayClassReminder2:
        await this.sendMsg(task.userId, task.chatId, [scripts.second_day_class_reminder_2.content, scripts.second_day_class_reminder_2_image], scripts.second_day_class_reminder_2.description)
        break

      case TaskName.SecondClassEndFeedback:
        ChatStateStore.update(task.chatId, {
          nextStage: MoerNode.CourseFeedBackDay2
        })
        await this.sendMsg(task.userId, task.chatId, [scripts.second_class_end_feedback_1.content, scripts.second_class_end_feedback_2.content], scripts.second_class_end_feedback_1.description)
        break

      case TaskName.GoodNightMessage:
        await this.sendMsg(task.userId, task.chatId, [scripts.good_night_wx_passage, scripts.good_night_message.content], scripts.good_night_message.description)
        break

      case TaskName.CourseCompleteAwardAndFeedback:
        if (taskTime.day === 2) {
          ChatStateStore.update(task.chatId, {
            nextStage: MoerNode.CourseFeedBackDay1
          })

          const isCompleteClass = await DataService.isCompletedCourse(task.chatId, { day: 1 })
          const isCompleteClassRecording = await DataService.isCompletedCourse(task.chatId, {
            day: 1,
            is_recording: true
          })
          const hasSendClass1Reward = ChatStateStore.get(task.chatId).state.has_send_class1_reward

          if (!isCompleteClass && !isCompleteClassRecording) { // 第一天的课程未完课
            await this.sendMsg(task.userId, task.chatId, [await scripts.first_day_class_not_complete.content(task.chatId), scripts.first_day_class_not_complete_push.content], scripts.first_day_class_not_complete.description)
          }

          if ((isCompleteClass || isCompleteClassRecording) && !hasSendClass1Reward) { // 第一天的课程 完课
            await this.sendMsg(task.userId, task.chatId, [scripts.first_day_class_complete.content, scripts.first_day_course_complete_reward_link], scripts.first_day_class_complete.description)
          }
        }

        if (taskTime.day === 3) {
          ChatStateStore.update(task.chatId, {
            nextStage: MoerNode.CourseFeedBackDay2
          })

          const inClass = await DataService.isInClass(task.chatId, { day: 2 })
          const hasSendClass2Reward = ChatStateStore.get(task.chatId).state.has_send_class2_reward
          const isCompleteClass2 = await DataService.isCompletedCourse(task.chatId, { day: 2 })
          const isInAnyClass = await DataService.isInAnyClass(task.chatId)

          if (!inClass) {
            const recordingLink = await DataService.getCourseLink(2, task.chatId, true)
            await this.sendMsg(task.userId, task.chatId, scripts.recording_day_2_remind2_link(recordingLink), scripts.recording_day_2_remind2.description)
            break
          }

          if (isCompleteClass2 && !hasSendClass2Reward) {
            await this.sendMsg(task.userId, task.chatId, [scripts.day2_class_complete_reward.content, scripts.remind_recording_day_2_complete_mp3_feishu], scripts.remind_recording_day_3_complete.description)
          }

          // 前两天的课程未到课
          if (!isInAnyClass) {
            await this.sendMsg(task.userId, task.chatId, scripts.second_day_class_not_complete_push.content, scripts.second_day_class_not_complete_push.description)
          }
        }

        if (taskTime.day === 4) {
          // 周四 催 第三节课 完课/未完课
          const isCompleteClass3 = await DataService.isCompletedCourse(task.chatId, { day: 3 })
          const hasSendClass3Reward = ChatStateStore.get(task.chatId).state.has_send_class3_reward

          // 第三节 完课
          if (isCompleteClass3 && !hasSendClass3Reward) {
            await this.sendMsg(task.userId, task.chatId, [scripts.send_recording_day_4_complete_1.content, scripts.send_recording_day_4_complete_link, scripts.send_recording_day_4_complete_mp3], scripts.send_recording_day_4_complete_1.description)
          } else {
            await this.sendMsg(task.userId, task.chatId, [await scripts.send_recording_day_4_not_complete.content(task.chatId)], scripts.send_recording_day_4_not_complete.description)
          }
        }

        // TODO 直播间问题解答
        // await this.sendMsg(task.userId, task.chatId, scripts.course_complete_award_and_feedback.content, scripts.course_complete_award_and_feedback.description)
        break

      case TaskName.RedShoesSharing:
      {
        const isCompleteClass2 = await DataService.isCompletedCourse(task.chatId, { day: 2 }) || await DataService.isCompletedCourse(task.chatId, { day: 2, is_recording: true })
        if (!isCompleteClass2) {
          await this.sendMsg(task.userId, task.chatId, [scripts.wednesday_1622_1, await scripts.wednesday_1622_2.content(task.chatId)], scripts.red_shoes_sharing.description)
        }
        break
      }

      case TaskName.ThirdDayClassReminder:
      { await this.sendMsg(task.userId, task.chatId, [scripts.third_day_class_reminder_1.content, scripts.extra_class_notification_2_feishu, scripts.third_day_class_reminder_2.content ], scripts.third_day_class_reminder_1.description)
        break }

      case TaskName.ThirdDayClassReminder1:
        await this.sendMsg(task.userId, task.chatId, [await scripts.third_day_class_reminder1.content(task.chatId), scripts.third_day_class_reminder3], scripts.third_day_class_reminder1.description)
        break

      case TaskName.ThirdDayClassReminder2:
        await this.sendMsg(task.userId, task.chatId, [await scripts.third_day_class_reminder2.content(task.chatId), scripts.third_day_class_reminder2_image], scripts.third_day_class_reminder2.description)
        break

      case TaskName.ThirdDayStartNotification:
      { const liveLink_third = await DataService.getCourseLink(3, task.chatId)
        await this.sendMsg(task.userId, task.chatId, scripts.third_day_start_notification_link(liveLink_third), scripts.third_day_start_notification.description)
        break }

      case TaskName.SalesPitch:
      // 已付款下单不发送营销信息
      { const isPaid = await DataService.isPaidSystemCourse(task.chatId)
        if (isPaid) {
          break
        }

        let inClass: boolean
        let isCompleteClass: boolean
        try {
          inClass = await DataService.isInClass(task.chatId, { day: 3 })
          isCompleteClass = await DataService.isCompletedCourse(task.chatId, { day: 3 })
        } catch (e) {
          logger.error(e)
          inClass = false
          isCompleteClass = false
        }

        console.log(inClass, isCompleteClass)

        if (inClass || isCompleteClass) {
          ChatStateStore.update(task.chatId, {
            nextStage: MoerNode.CourseFeedBackDay3InClass
          })
          await CourseFeedBackDay3InClass.invoke(await getState(task.chatId, task.userId))
        } else {
          ChatStateStore.update(task.chatId, {
            nextStage: MoerNode.CourseFeedBackDay3NotInClass
          })
          await this.sendMsg(task.userId, task.chatId, [scripts.sales_pitch_not_in_class_1.content, scripts.sales_pitch_not_in_class_2.content], scripts.sales_pitch_not_in_class_1.description)
        }
        break }

      case TaskName.FunctionEmphasis: {
        // 已付款下单不发送营销信息
        const isPaid = await DataService.isPaidSystemCourse(task.chatId)
        if (isPaid) {
          break
        }

        await this.sendMsg(task.userId, task.chatId, scripts.function_emphasis.content, scripts.function_emphasis.description)
        break
      }

      case TaskName.ExtraClassResearch:
        await this.sendMsg(task.userId, task.chatId, [scripts.extra_class_research_1.content, scripts.extra_class_research_2.content], scripts.extra_class_research_1.description)
        break

      case TaskName.ExtraClassNotification:
        if (!await DataService.isCompletedCourse(task.chatId, { day: 3 })) {
          await this.sendMsg(task.userId, task.chatId, [scripts.extra_class_notification_1.content, scripts.extra_class_notification_2.content, scripts.extra_class_notification_3], scripts.extra_class_notification_1.description)
        }
        break
      case TaskName.LayingOutSummaryYesterday:
      { const isCompleteCourse = await DataService.isCompletedCourse(task.chatId, { day:3, is_recording:false })
        const isCompleteCourseRecording = await DataService.isCompletedCourse(task.chatId, { day:3, is_recording:true })
        if (isCompleteCourseRecording || isCompleteCourse) break
        await this.sendMsg(task.userId, task.chatId, [await scripts.laying_out_summary_yesterday_1.content(task.chatId)], scripts.laying_out_summary_yesterday_1.description)
        break }

      case TaskName.ScheduleExtraClass:
        await this.sendMsg(task.userId, task.chatId, [await scripts.schedule_extra_class.content(task.chatId), scripts.schedule_extra_class_video], scripts.schedule_extra_class.description)
        break

      case TaskName.CountdownToClass:
        await this.sendMsg(task.userId, task.chatId, [await scripts.countdown_to_class.content(task.chatId), scripts.countdown_to_class_video], scripts.countdown_to_class.description)
        break

      case TaskName.ThirdClassReminder:
        await this.sendMsg(task.userId, task.chatId, await scripts.fourth_class_reminder.content(task.chatId), scripts.fourth_class_reminder.description)
        break

      case TaskName.FinalMarketingPitch: {
        // 已付款下单不发送营销信息
        const isPaid = await DataService.isPaidSystemCourse(task.chatId)
        if (isPaid) {
          break
        }

        const isInAnyClass = await DataService.isInAnyClass(task.chatId)
        if (!isInAnyClass) {
          await this.sendMsg(task.userId, task.chatId, [scripts.final_marketing_pitch.content, scripts.final_marketing_pitch_image], scripts.final_marketing_pitch.description)

        } else {

          const userSlots = await ContextBuilder.getCustomerPortrait(task.chatId)

          if (userSlots) {
            const llmRes = await LLM.predict(`参考之前客户的需求和痛点

## 客户画像
${userSlots} 

背景信息：
当前客户已经完成了5天冥想入门体验营，你正在给客户推荐21天课程
任务：
根据之前客户的需求和痛点, 提供后续21天系统课程对客户针对性带来的好处。

例如:
"到此，我们全部直播课就结束了哦，之前沟通的咱们想 xxx，看咱们课程和作业都认真完成了，不知道咱们这几天学习下来感觉怎么样？"
输出以"到此，我们全部直播课就结束了哦"开头，以"不知道咱们这几天学习下来感觉怎么样？"结束。输出不要超过三句话，简短，清晰。`)

            await MessageSender.sendById({
              chat_id: task.chatId,
              user_id: task.userId,
              ai_msg: removeMarkdown(llmRes)
            })
          } else {
            await MessageSender.sendById({
              chat_id: task.chatId,
              user_id: task.userId,
              ai_msg: '到此，我们全部直播课就结束了哦，之前看咱们课程和作业都认真完成了，不知道咱们这几天学习下来感觉怎么样？因为体验课时间有限，如果需要更细化学习，还是需要参加我们的21天系统班课程。在系统班中，我们会有更专业的指导和更完整的课程体系，更有利于帮助您养成良好的习惯，真正实现持久的改变和提升。'
            })
          }

          await sleep(8 * 1000)
          await this.sendMsg(task.userId, task.chatId, '咱们现在遇到什么卡点了不，我们可以一起来看看怎么突破？距离咱们唐宁老师专属1000元优惠券过期还有最后2小时')

          await sleep(5 * 1000)
          await MessageSender.sendById({
            chat_id: task.chatId,
            user_id: task.userId,
            ai_msg:'[课程脉络图]',
            send_msg:{
              type: IWecomMsgType.Image,
              url:'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/moer/%E8%AF%BE%E7%A8%8B%E8%84%89%E7%BB%9C%E5%9B%BE.jpg'
            }
          })
        }

        break
      }

      case TaskName.PaymentAbilityReminder: {
        // 已付款下单不发送营销信息
        const isPaid = await DataService.isPaidSystemCourse(task.chatId)
        if (isPaid) {
          break
        }

        await this.sendMsg(task.userId, task.chatId, scripts.payment_ability_reminder.content, scripts.payment_ability_reminder.description)
        break
      }

      case TaskName.AdditionalMarketing: {
        const isPaid = await DataService.isPaidSystemCourse(task.chatId)
        if (isPaid) {
          break
        }

        await this.sendMsg(task.userId, task.chatId, scripts.additional_marketing.content, scripts.additional_marketing.description)
        break
      }

      case TaskName.CompletionReward: {
        const isPaid = await DataService.isPaidSystemCourse(task.chatId)
        const paymentFailure = ChatStateStore.getFlags(task.chatId).handled_failed_payment
        if (isPaid) {
          await this.sendMsg(task.userId, task.chatId, [scripts.fifth_day_complete_payment.content, scripts.fifth_day_complete_payment_pdf], scripts.fifth_day_complete_payment.description)
          break
        }
        await this.sendMsg(task.userId, task.chatId, [scripts.fifth_day_not_complete_payment_1.content, scripts.fifth_day_complete_payment_pdf], scripts.fifth_day_not_complete_payment_1.description)
        if (paymentFailure) {
          await this.sendMsg(task.userId, task.chatId, scripts.fifth_day_not_complete_payment_3.content, scripts.fifth_day_not_complete_payment_3.description)
          break
        }
        await this.sendMsg(task.userId, task.chatId, scripts.fifth_day_not_complete_payment_2.content, scripts.fifth_day_not_complete_payment_2.description)
        break
      }

      case TaskName.CampEndReminder: {
        const isPaid = await DataService.isPaidSystemCourse(task.chatId)
        if (isPaid) {
          break
        }

        if (taskTime.post_course_week === 1) { // 完课周后 周一
          if (taskTime.day === 1) {
            if (taskTime.time === '11:30:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.post_course_week1_day1_reminder1_emoticon as ISendMedia), (scripts.post_course_week1_day1_reminder1_text as ISendWrapper<string>).content], (scripts.post_course_week1_day1_reminder1_text as ISendWrapper<string>).description)
            } else if (taskTime.time === '17:20:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.post_course_week1_day1_reminder2_text as ISendWrapper<string>).content, (scripts.post_course_week1_day1_reminder2_image1) as ISendMedia, (scripts.post_course_week1_day1_reminder2_image2) as ISendMedia], (scripts.post_course_week1_day1_reminder2_text as ISendWrapper<string>).description)
            } else if (taskTime.time === '21:20:00') {
              await this.sendMsg(task.userId, task.chatId, [(scripts.post_course_week1_day1_reminder3_text as ISendWrapper<string>).content], (scripts.post_course_week1_day1_reminder3_text as ISendWrapper<string>).description)
            }
          }
        } else if (taskTime.day === 5) {
          if (taskTime.time === '16:20:00') {
            await this.sendMsg(task.userId, task.chatId, [scripts.fifth_day_camp_end_reminder_1.content, scripts.fifth_day_camp_end_reminder_channel], scripts.fifth_day_camp_end_reminder_1.description)
          } else if (taskTime.time === '17:30:00') {
            await this.sendMsg(task.userId, task.chatId, [scripts.fifth_day_camp_end_reminder_2.content, scripts.fifth_day_camp_end_reminder_2_image], scripts.fifth_day_camp_end_reminder_2.description)
          } else if (taskTime.time === '20:20:00') {
            await ChatStatStoreManager.initState(task.chatId)
            const userPortrait = await ContextBuilder.getCustomerPortrait(task.chatId)
            if (userPortrait) {
              const name = await DataService.getWechatName(task.userId)
              const llmRes = await LLM.predict(`# 分手信
你是麦子老师，刚刚花费了一周多的时间服务了入门营客户${name}，但是他没有下单21天系统课，你感到非常遗憾。
你需要根据客户画像写一封分手信，希望客户能够看到信后非常感动，来找你买冥想课，你要用各种心里技巧，把真诚淋漓尽致的展现出来，用些emoji表情，用真诚打动客户！

## 你需要大致分下面几个部分完成分手信，也可以根据客户情况进行删改：
1. 一句话主题开头，直击人心
  如：XXX，我感受到您的疲惫与渴望❤️
2. 看见：结合客户分享与经历，深度共情
  如：每次想到您跟我分享的生活状态，我的心都会揪一下。工作到凌晨3点的疲惫、无法兼顾时间的压力，以及在冥想中流下的眼泪，这些都让我看到了一个努力坚持但也背负着巨大压力的您🥹。
3. 鼓励：肯定客户的努力，愿意尝试冥想
  如：作为一个陪伴您的朋友和指导者，我真的为您心疼，同时也感到敬佩——您在如此忙碌的生活中，依然愿意尝试冥想，寻找属于自己的内在平静和智慧🌟。
4. 转折：结合客户痛点推荐系统课
  如：我知道，时间对您来说非常珍贵，推掉工作来上课可能让您感到挣扎。但XXX，您可曾想过，这门课不仅仅是一个“任务”，它或许是一把钥匙，可以打开一个充满能量和智慧的世界，让您用更从容的心态面对生活的挑战💪。还记得您提到的那些流泪的时刻吗？那些不是脆弱，而是一种释放，一种灵魂深处的触动。或许正是在这些瞬间，您的内心深处在告诉您：是时候为自己而活了。
5. 深表遗憾，并尊重选择
  如：我真的很遗憾[流泪]，没能更好地帮助您找到一个更轻松的方式融入这段旅程。如果这一刻，您仍然觉得需要更多时间或空间，我完全理解，并尊重您的选择❤️。
6. 重回主题，强推系统班
  如：但我也想说，这门课是一个工具，而不是一种负担。它可以随着您的节奏而调整，而我的存在，就是为了帮助您找到适合您的方式去探索它🙌。最后，我只想对您说：您并不孤单。无论您是否继续这门课程，我都会在这里，为您提供支持，帮助您走向更平静、更觉知的未来。XXX，如果您有任何问题，我随时都在，等您的一句话❤️。
7. 落款，结合客户经历，一句话祝福+麦子老师
  如：------愿您永远找到属于自己的光🙏，麦子老师

## 客户昵称
${name}

## 客户画像
${userPortrait}
请根据以上信息完成任务！千万不要输出每一部分的标题，只输出内容即可，并用一句话主题开始：`)
              await MessageSender.sendById({
                chat_id: task.chatId,
                user_id: task.userId,
                ai_msg: removeMarkdown(llmRes)
              }, {
                shortDes: '课程结营后给客户发送的走心祝福类信件'
              })
            } else {
              await this.sendMsg(task.userId, task.chatId, [scripts.fifth_day_camp_end_reminder_3.content, scripts.fifth_day_review_share_image], scripts.fifth_day_camp_end_reminder_3.description)
            }
          } else if (taskTime.time === '21:30:00') {
            await this.sendMsg(task.userId, task.chatId, [scripts.fifth_day_camp_end_reminder_5.content, scripts.fifth_day_cheap_price_image], scripts.fifth_day_camp_end_reminder_5.description)
          }
        } else if (taskTime.day === 6) {
          if (taskTime.time === '10:00:00') {
            await this.sendMsg(task.userId, task.chatId, [scripts.sixth_day_camp_end_reminder_2.content, scripts.sixth_day_chat_record_image], scripts.sixth_day_camp_end_reminder_2.description)
          } else if (taskTime.time === '12:01:00') {
            await this.sendMsg(task.userId, task.chatId, [scripts.sixth_day_camp_end_reminder_3.content, scripts.sixth_day_feedback_image], scripts.sixth_day_camp_end_reminder_3.description)
          } else if (taskTime.time === '18:00:00') {
            await this.sendMsg(task.userId, task.chatId, scripts.sixth_day_camp_end_reminder_4.content, scripts.sixth_day_camp_end_reminder_4.description)
          } else if (taskTime.time === '20:00:00') {
            await this.sendMsg(task.userId, task.chatId, scripts.sixth_day_camp_end_reminder_5.content, scripts.sixth_day_camp_end_reminder_5.description)
          } else if (taskTime.time === '22:00:00') {
            await this.sendMsg(task.userId, task.chatId, [scripts.sixth_day_camp_end_reminder_6.content, scripts.sixth_day_group_chat_image_1, scripts.sixth_day_group_chat_image_2], scripts.sixth_day_camp_end_reminder_6.description)
          }
        } else if (taskTime.day === 7) {
          if (taskTime.time === '10:00:00') {
            await this.sendMsg(task.userId, task.chatId, [scripts.seventh_day_camp_end_reminder_1.content, scripts.seventh_day_peoples_paper_image], scripts.seventh_day_camp_end_reminder_1.description)
          } else if (taskTime.time === '14:00:00') {
            await this.sendMsg(task.userId, task.chatId, [scripts.seventh_day_camp_end_reminder_2.content, scripts.seventh_day_senior_feedback], scripts.seventh_day_camp_end_reminder_2.description)
          } else if (taskTime.time === '17:00:00') {
            await this.sendMsg(task.userId, task.chatId, [scripts.seventh_day_camp_end_reminder_3.content, (scripts.seventh_day_camp_end_reminder_3_1 as ISendWrapper<string>).content, (scripts.seventh_day_camp_end_reminder_3_image as ISendMedia)], scripts.seventh_day_camp_end_reminder_3.description)
          } else if (taskTime.time === '19:00:00') {
            // 待支付
            if (ChatStateStore.getFlags(task.chatId).handled_failed_payment) {
              await this.sendMsg(task.userId, task.chatId, '咱们保留赠品名额（坐垫+144季卡+笔记），晚上就过期了，组长问我您还需要吗？', scripts.seventh_day_camp_end_reminder_4.description)
            } else {
              await this.sendMsg(task.userId, task.chatId, '晚上12点咱们这期的入门营优惠时间就彻底结束了哈，咱们是确定不加入了吗？', scripts.seventh_day_camp_end_reminder_4.description)
            }
          } else if (taskTime.time === '21:00:00') {
            const wechatName = await DataService.getWechatName(task.userId)

            await this.sendMsg(task.userId, task.chatId, await scripts.seventh_day_camp_end_reminder_5.content(wechatName), scripts.seventh_day_camp_end_reminder_5.description)
          }
        }
        break
      }

      default:
        console.log(`Unhandled task: ${task.name}`)
        break
    }
  }
}