import { BaseTask, getState, trackProcess } from './baseTask'
import { ITask } from '../../../schedule/type'
import { getScript } from '../../../script/script'
import { ChatStateStore } from '../../../../storage/chat_state_store'
import { SilentReAsk } from '../../../schedule/silent_requestion'
import { LLMNode } from '../../nodes/llm'
import { DataService } from '../../../../getter/getData'
import { EnergyTestAnalyze } from '../../nodes/energyTestAnalyze'
import logger from '../../../../../../model/logger/logger'
import { sleep } from '../../../../../../lib/schedule/schedule'
import { ChatHistoryService } from '../../../chat_history/chat_history'
import { AsyncLock } from '../../../../../../lib/lock/lock'
import { Config } from '../../../../../../config/config'

export class HandleEnergyTest extends BaseTask {

  @trackProcess
  public async process(task: ITask): Promise<void> {
    const { userId, chatId } = task
    const day0Scripts = getScript().pre_course_day

    // if (ChatStateStore.getFlags(chatId).is_complete_energy_test_analyze) {
    //   return
    // }

    const score = await DataService.getEnergyTestScore(chatId)
    if (score === null) {
      logger.warn({ chat_id: chatId }, '获取不到能量测评')
      return
    }

    // 先进行解读
    await this.getAnalyze(score, userId, chatId)

    const currentTime = await DataService.getCurrentTime(chatId)

    if (!currentTime.is_course_week && ChatStateStore.getFlags(chatId).is_complete_energy_test_analyze) {
      // 5 分钟后发送冥想视频和 app 下载链接
      await SilentReAsk.schedule(task.chatId, async () => {
        const lock = new AsyncLock() // 等待主线回复完再执行

        await lock.acquire(task.chatId, async () => {
          await this.sendMsg(userId, chatId, [day0Scripts.complete_energy_test_1.content, day0Scripts.complete_energy_test_2.content, day0Scripts.complete_energy_test_3, day0Scripts.app_download_remind.content, day0Scripts.app_download_photo], day0Scripts.complete_energy_test_1.description)
        }, { timeout: 3 * 60 * 1000 })
      }, 5 * 60 * 1000, {
        auto_retry: true,
        independent: true
      })
    }
  }


  private async getAnalyze(score: number, userId: string, chatId: string) {
    let explanation = ''
    if (score <= 0) {
      explanation = `当你发现自己在这个评分区间里，可能意味着你正在经历某些令人心痛的时刻，比如工作上的不确定性、家庭关系的紧张，或是健康的挑战。这些试炼可能会激起内心深处的痛苦情绪，如羞耻、罪恶感或恐惧，感觉自己正被这些沉重的感觉压垮。
改善建议： 在这样的时刻，冥想可以成为一个非常有力的工具。它不仅能帮助你找到一种内在的平和，更重要的是，它能帮助你提升个人的能量频率。通过冥想，你可以开始放下这些沉重的情绪包袱，发现面对挑战时的内在力量。当你开始探寻这些情绪是如何影响你的能量状态时，你就能慢慢地用更积极、更强大的情绪来取代它们。这种变化会逐渐地作用于你的外部世界，帮助你找到克服困难的新路径。
记住，冥想不是逃避现实的手段，而是一种深入自我、理解复杂情绪的方式，并通过提升你的能量频率来改变你的生活质量。正是这些渐进的内在变化，会逐步促成你生活中的积极转变。`
    } else if (score > 0 && score <= 60) {
      explanation = `如果你的能量频率落在这个范围，你可能会觉得自己经常处于忧虑、愤怒或批判的情绪中。这些感觉可能会让你有时候感到被困住了，尽管你也有机会体验到积极的情绪。你可能会发现自己在寻求成长和进步的道路上遇到了障碍。
改善建议： 此时冥想不只是一个静心的实践，它是你自我发现之旅的一部分，能助你深入理解这些复杂的情绪。通过冥想，你可以逐渐学会将注意力从这些消极情绪转移到积极的行动上。这个过程会慢慢帮助你提升能量频率，使你能够更常体验到如勇气、中立和接受等更平和的情绪。`
    } else if (score > 60 && score <= 200) {
      explanation = `现在，你能够更加冷静地应对生活中的起伏。你可能已经认识到，过度担忧未来或纠结于过去都无助于解决问题，开始更多地活在当下。你的这种态度可能会在职场中得到体现，例如，面对紧急情况时，你能够保持冷静，理性地安排任务和资源，而不是被情绪所左右。
改善建议：在提升自己的能量频率的过程中，你可以继续探索不同类型的冥想，比如行走冥想或者音乐冥想，这些都能帮助你进一步释放内在的紧张和恐惧。同时，在日常生活中，尽量练习感激之心，比如通过感恩日记来记录每天你感到感激的事情。这样的实践可以增强你的积极情绪，使你更容易达到更高的能量频率状态。`
    } else if (score > 200 && score <= 300) {
      explanation = `在这个频率层级，你的生活态度通常是积极的，你不仅对自己充满了爱和自信，也对周围的人表现出同样的情感。举一个场景，当朋友或同事面临困难时，你可以成为他们信赖的支持者，用你的积极态度鼓励他们，帮助他们看到问题的另一面。在家庭中，你可能是和解者的角色，在争议出现时，你能够用你的智慧和同情心引导对话到一个更加建设性的方向。
改善建议：为了保持和提升这一能量频率，你可以尝试参与团体冥想或社区服务，这能够帮助你将个人的积极能量传递给更多人。同时，定期进行自我反思，以保持你的内在平衡，并确保你的行为与你的高能量价值观一致。`
    } else if (score > 300) {
      explanation = `在这个层次，你可能会体验到深刻的内在和谐和爱，甚至在面对生活中的挑战时也能够保持一种平和和接纳的态度。假设你遇到了一个个人或职业上的重大变动，比如失业或家庭困境，你有能力不陷入绝望，而是从这些挑战中看到成长和学习的机会。
改善建议：要维持这一频率，你可以通过深入的冥想实践来探索和连接更高的自我。你也许会发现自己对哲学、心理学或修行的深入学习非常感兴趣，这些都是提升意识和进一步提高能量频率的方式。`
    }

    const positiveEnergy = `一般200以上为正能量
例如：咱们目前的分值是${score}，一般200以上为正能量，目前咱们这分数处于正能量范围内。`
    const negativeEnergy = `一般200以下为负能量
例如：咱们目前的分值是${score}，200以下就是负能量了，目前咱们这分数处于负能量范围内。`

    // 添加分数到上下文
    const energyAnalyzeSupplement = `你需要对客户做的能量层级评测的分数进行分析。
参考以下内容进行回复：
出题逻辑：根据大卫·R·霍金斯的能量频率层级理论，每个人的意识水平可以通过他们对生活中各种情境的反应来表现。这套题目通过询问个人在特定情境下的反应，旨在揭示个人在霍金斯能量频率层级中的大致位置

客户分数：${score}

该分数解读：${explanation}

解读过程参考以下几个步骤：
（1）先整体告知客户当前的分值处于什么概念，让客户先对自己定位有个总结概念。${score >= 200 ? positiveEnergy : negativeEnergy}
（2）其次告知对应分值的具体解读。
（3）参考对应分析建议，输出更具体的建议给客户。
"班班仔细看了下咱们测评，给咱们具体解读下，量表的总分是504，你的能量分数是 " 作为回复的开始`

    if (!Config.setting.localTest) {
      await sleep(2 * 60 * 1000)
    }

    // 如果客户消息 包含了 相同的分数，不再进行解读
    const userMessages = (await ChatHistoryService.getUserMessages(chatId)).join('\n')
    if (userMessages.includes(`老师，我的能量测评分数是：${score}`)) {
      return
    }

    await ChatHistoryService.addUserMessage(chatId, `老师，我的能量测评分数是：${score}`)

    await LLMNode.invoke({
      state: await getState(chatId, userId),
      talkStrategyPrompt: energyAnalyzeSupplement,
      noSplit: true,
      notCheckRepeat: true,
      noInterrupt: true,
      customerChatRounds: 0,
      noCheckHumanInvolved: true,
      noStagePrompt: true
    })

    await EnergyTestAnalyze.invoke(await getState(chatId, userId))
  }
}