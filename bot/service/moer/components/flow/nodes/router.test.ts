import { Router } from './router'

describe('Router - 问卷识别逻辑测试', () => {
  describe('isRepliedSurvey - 统一数字拆分逻辑', () => {
    it('应该正确识别 129 为有效问卷', async () => {
      // 129 → [1, 2, 9]
      // 生活角色: 1, 2 (1-4区间) ✓
      // 冥想经验: 无 (5-7区间) ✗
      // 人生议题: 9 (8-13区间) ✓
      // 两个区间有数字，应该返回 true
      const result = await Router.isRepliedSurvey('129')
      expect(result).toBe(true)
    })

    it('应该正确识别 912 为无效问卷', async () => {
      // 912 → [9, 12]
      // 生活角色: 无 (1-4区间) ✗
      // 冥想经验: 无 (5-7区间) ✗
      // 人生议题: 9, 12 (8-13区间) ✓
      // 只有一个区间有数字，应该返回 false
      const result = await Router.isRepliedSurvey('912')
      expect(result).toBe(false)
    })

    it('应该正确识别包含三个区间的有效问卷', async () => {
      // 159 → [1, 5, 9]
      // 生活角色: 1 (1-4区间) ✓
      // 冥想经验: 5 (5-7区间) ✓
      // 人生议题: 9 (8-13区间) ✓
      // 三个区间都有数字，应该返回 true
      const result = await Router.isRepliedSurvey('159')
      expect(result).toBe(true)
    })

    it('应该正确识别包含两个区间的有效问卷', async () => {
      // 测试不同的两个区间组合
      expect(await Router.isRepliedSurvey('15')).toBe(true)  // 生活角色 + 冥想经验
      expect(await Router.isRepliedSurvey('18')).toBe(true)  // 生活角色 + 人生议题
      expect(await Router.isRepliedSurvey('58')).toBe(true)  // 冥想经验 + 人生议题
    })

    it('应该正确识别只有一个区间的无效问卷', async () => {
      // 只有一个区间的情况都应该返回 false
      expect(await Router.isRepliedSurvey('12')).toBe(false)   // 只有生活角色
      expect(await Router.isRepliedSurvey('56')).toBe(false)   // 只有冥想经验
      expect(await Router.isRepliedSurvey('89')).toBe(false)   // 只有人生议题
    })

    it('应该正确处理中文数字', async () => {
      // 测试中文数字的识别
      expect(await Router.isRepliedSurvey('一五九')).toBe(true)
      expect(await Router.isRepliedSurvey('二六十')).toBe(true)
      expect(await Router.isRepliedSurvey('三七十一')).toBe(true)
    })

    it('应该正确处理混合数字格式', async () => {
      // 测试阿拉伯数字和中文数字混合
      expect(await Router.isRepliedSurvey('1五9')).toBe(true)
      expect(await Router.isRepliedSurvey('二6十')).toBe(true)
    })

    it('应该过滤掉非问卷内容', async () => {
      // 测试非问卷模式的过滤
      expect(await Router.isRepliedSurvey('三天还是五天')).toBe(false)
      expect(await Router.isRepliedSurvey('怎么样')).toBe(false)
      expect(await Router.isRepliedSurvey('好不好')).toBe(false)
      expect(await Router.isRepliedSurvey('还是或者')).toBe(false)
    })

    it('应该过滤掉手机号', async () => {
      // 测试手机号的过滤（假设 RegexHelper.extractPhoneNumber 能正确识别）
      expect(await Router.isRepliedSurvey('13812345678')).toBe(false)
    })

    it('应该处理复杂的真实场景', async () => {
      // 模拟真实客户输入
      expect(await Router.isRepliedSurvey('我选择129')).toBe(true)
      expect(await Router.isRepliedSurvey('答案是：1、5、9')).toBe(true)
      expect(await Router.isRepliedSurvey('1-5-9')).toBe(true)
      expect(await Router.isRepliedSurvey('1 5 9')).toBe(true)
    })
  })
})
