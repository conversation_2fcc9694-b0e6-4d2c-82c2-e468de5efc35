import logger from '../../../../model/logger/logger'
import { ContextBuilder } from './context'
import { EventTracker, IEventType } from '../../../../model/logger/data_driven'
import { getBotId } from '../../../../config/chat_id'
import { getPrompt } from './prompt'
import { IWorkflowState } from '../flow/flow'
import { LLM } from '../../../../lib/ai/llm/LLM'
import { MetaActionStage } from '../meta_action/meta_action_router'
import { SalesNodeHelper } from '../flow/helper/salesNodeHelper'

function hashChatId(chatId: string): number {
  let hash = 0
  for (let i = 0; i < chatId.length; i++) {
    hash = (hash * 31 + chatId.charCodeAt(i)) >>> 0  // 保持非负整数
  }
  return hash
}

export class FreeThink {
  public static async invoke(state: IWorkflowState, metaActionStage: MetaActionStage) {
    const freeThinkPrompt = await getPrompt('free-think')
    const model = (hashChatId(state.chat_id) % 2 === 1) || getBotId(state.chat_id) === '1688855694714276' ? 'freethink-1' : 'gpt-4.1'
    const customerBehavior = await ContextBuilder.getCustomerBehavior(state.chat_id)
    const customerPortrait = await ContextBuilder.getCustomerPortrait(state.chat_id)
    const dialogHistory = await SalesNodeHelper.getChatHistory(state.chat_id, 6, 18)
    const temporalInformation = await ContextBuilder.getTimeInformation(state.chat_id)
    await state.interruptHandler.interruptCheck()

    const output = await LLM.predict(
      freeThinkPrompt, {
        model: model, // model, gpt-4.1, freethink-1
        responseJSON: true,
        meta: {
          promptName: 'free_think',
          chat_id: state.chat_id,
          round_id: state.round_id
        } }, {
        thinkPrompt: metaActionStage.thinkPrompt,
        metaActions: metaActionStage.metaActions,
        customerBehavior: customerBehavior,
        customerPortrait: customerPortrait,
        dialogHistory: dialogHistory,
        temporalInformation: temporalInformation,
      })

    let think: string = ''
    let action: string[] = []
    let strategy: string = '正常回复'
    let content: string = ''

    try {
      const parsedOutput = JSON.parse(output)
      think = parsedOutput.think
      action = parsedOutput.action
      strategy = parsedOutput.strategy
      content = parsedOutput.content
    } catch (error) {
      logger.error('FreeThink 解析 JSON 失败:', error)
    }
    EventTracker.track(state.chat_id, IEventType.FreeThink, { round_id: state.round_id, think: think, action: JSON.stringify(action), strategy: strategy, content: content })
    logger.debug({ chat_id: state.chat_id, round_id: state.round_id }, `think: ${think}\naction: ${JSON.stringify(action)}\nstrategy: ${strategy}`)
    return { think, action, strategy, content }
  }
}