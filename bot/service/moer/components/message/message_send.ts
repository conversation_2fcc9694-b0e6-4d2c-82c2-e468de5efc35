import { ChatHistoryService } from '../chat_history/chat_history'
import { sleep } from '../../../../lib/schedule/schedule'
import { Config } from '../../../../config/config'
import { JuziAPI } from '../../../../lib/juzi/api'
import { IWecomMessage, IWecomMsgType } from '../../../../lib/juzi/type'
import { UUID } from '../../../../lib/uuid/uuid'

interface IWechatSendMsg {
    user_id?: string
    room_id?: string // 群聊的 id
    chat_id: string
    ai_msg: string // 放到聊天记录中的文本信息，在发送纯文本情况下与 send_msg 相同。 当发送文件或资料时，此处有可能是占位符，可以单独设置 send_msg 为 [xx文件.txt] 之类。
    send_msg?: IWecomMessage // 发送的非文本信息
}

interface IWechatSendOptions {
  notAddBotMessage?: boolean // 是否将聊天记录添加到数据库
  shortDes?: string // 简短描述
  isAnnouncement?: boolean // 群公告

  round_id?: string // 轮次 id, 用于记录模型输出
  sop_id?:string
}

export class MessageSender {

  public static async sendById(msg: IWechatSendMsg, options?: IWechatSendOptions) {
    console.log(msg)
    if (!msg.ai_msg || msg.ai_msg.trim() === '') {
      return
    }

    const externalRequestId = UUID.short()

    if (Config.setting.localTest) {
      // add Bot Message 中会 Print 消息
    } else {
      let sendMsg: IWecomMessage
      if (!msg.send_msg) {
        sendMsg =  {
          type: IWecomMsgType.Text,
          text: msg.ai_msg
        }
      } else {
        sendMsg = msg.send_msg
      }


      let isAtAll : boolean| undefined = undefined
      if (sendMsg.type === IWecomMsgType.Text && sendMsg.mention && sendMsg.mention.includes('@all')) {
        delete  sendMsg.mention
        isAtAll = true
      }

      await JuziAPI.sendMsg({
        imBotId: Config.setting.wechatConfig?.id as string,
        imContactId: msg.user_id,
        imRoomId: msg.room_id,
        msg: sendMsg,
        isAtAll: isAtAll,
        isAnnouncement: options?.isAnnouncement,
        externalRequestId: externalRequestId
      })
    }

    if (options?.notAddBotMessage) {
      return
    }

    if (options?.shortDes && !options?.shortDes.startsWith('[')) {
      options.shortDes = `[${options.shortDes}]`
    }


    await ChatHistoryService.addBotMessage(msg.chat_id, msg.ai_msg, options?.shortDes, { round_id: options?.round_id, message_id: externalRequestId, sop_id:options?.sop_id })
  }

  public static getWxSendMessageFunction(chat_id: string, user_id: string) {
    // 返回一个异步函数，该函数将执行发送消息的行为
    return async (sentence: string, isFirstLine: boolean) => {
      if (!isFirstLine) { // 第一次回复除外
        await sleep(0.6 * sentence.length * 1000)
      }
      await MessageSender.sendById({ user_id: user_id, chat_id: chat_id, ai_msg: sentence })
    }
  }

}
