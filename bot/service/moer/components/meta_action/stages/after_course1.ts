import { MetaActionComponent } from '../meta_action_component'
import { Promise } from 'mongoose'
import { IActionInfo } from './post_action'
import { DataService } from '../../../getter/getData'
import { isScheduleTimeAfter } from '../../schedule/creat_schedule_task'
import { MetaActions, ThinkPrompt } from '../context'

export class AfterCourse1 extends MetaActionComponent {
  async isStageActive(chatId: string): Promise<boolean> {
    const currentTime = await DataService.getCurrentTime(chatId)
    const afterCourse1 = isScheduleTimeAfter(currentTime, { is_course_week: true, day: 1, time: '20:00:00' })
    return Promise.resolve(afterCourse1)
  }
  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return Promise.resolve(undefined)
  }

  getBaseGuidance(chatId: string): Promise<string> {
    return Promise.resolve('')
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.afterCourse1)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(ThinkPrompt.afterCourse1)
  }

  getGuidance(chatId: string, strategy: string, actions: string[], roundId: string): Promise<string> {
    return Promise.resolve('')
  }
}