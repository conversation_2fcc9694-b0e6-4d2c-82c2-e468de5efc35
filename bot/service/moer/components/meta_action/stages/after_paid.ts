import { MetaActionComponent } from '../meta_action_component'
import { IActionInfo } from './post_action'
import { DataService } from '../../../getter/getData'
import { MetaActions, ThinkPrompt } from '../context'

export class AfterPaid extends MetaActionComponent {

  async isStageActive(chatId: string): Promise<boolean> {
    const isPaid = await DataService.isPaidSystemCourse(chatId)
    return Promise.resolve(isPaid)
  }
  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    return Promise.resolve(null)
  }

  getBaseGuidance(chatId: string): Promise<string> {
    return Promise.resolve('\n注意：客户当前已购买冥想系统班')
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.afterPaid)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(ThinkPrompt.afterPaid)
  }

  getGuidance(chatId: string, strategy: string, actions: string[], roundId: string): Promise<string> {
    return Promise.resolve(this.getBaseGuidance(chatId))
  }
}
