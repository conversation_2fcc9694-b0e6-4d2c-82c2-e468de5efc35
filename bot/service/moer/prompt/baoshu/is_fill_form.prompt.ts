import { PromptTemplate } from '@langchain/core/prompts'

export class isFillFormPrompt {
  public static async format(query: string) {
    return PromptTemplate.fromTemplate(`You are tasked with determining whether a user has filled out a specific form based on the information they provide. Here is the form template:

<form_template>
姓名：
电话：
年级：
高考总分：
英语成绩：
（或）日语成绩：
所在城市：
想【国际本科/中外合办】还是【海外留学】：
本科总预算（学费+生活费）：
意向专业（文商科/理工科/艺术类）：
</form_template>

Your task is to analyze the following user input and determine if they have filled out the form above:

<user_input>
{user_input}
</user_input>

Follow these steps to complete the task:

1. Carefully read through the user input.
2. Compare the information provided in the user input to the fields in the form template.
3. Note that the user doesn't need to fill out all fields for it to be considered filled. Even if they've only provided information for one or two fields, it still counts as filling out the form.
4. Look for SPECIFIC and CLEAR information that directly matches the fields in the form template. Be strict about what constitutes valid form data:
   - 姓名: Must be a clear personal name (e.g., "张三", "李明")
   - 电话: Must be a phone number (e.g., "13812345678", "186-1234-5678")
   - 年级: Must specify educational level (e.g., "高三", "大二", "初中")
   - 高考总分/英语成绩/日语成绩: Must be specific scores (e.g., "580分", "120分")
   - 所在城市: Must be a clear city name (e.g., "北京", "上海", "杭州")
   - 留学意向: Must clearly indicate study abroad preference (e.g., "想出国留学", "国际本科")
   - 预算: Must specify budget amounts (e.g., "30万", "50万一年")
   - 意向专业: Must specify field of study (e.g., "商科", "理工科", "艺术类")
5. IGNORE vague, conversational, or unrelated content such as:
   - General questions (e.g., "三天还是五天", "怎么样", "好不好")
   - Casual conversation (e.g., "谢谢", "好的", "明白了")
   - Time-related queries that don't specify educational timeline
   - Numbers without clear context to form fields
6. If you find any SPECIFIC information that clearly corresponds to at least one field in the form template, consider the form as filled.
7. If you cannot find any SPECIFIC information that clearly matches any of the fields in the form template, consider the form as not filled.

Provide your answer in the following format:

<reasoning>
Reasoning: [Simply explain your reasoning here]
</reasoning>
<answer>
[YES/NO] - The form [is/is not] filled.
</answer>You are tasked with determining whether a user has filled out a specific form based on the information they provide. Here is the form template:

<form_template>
姓名：
电话：
年级：
高考总分：
英语成绩：
（或）日语成绩：
所在城市：
想【国际本科/中外合办】还是【海外留学】：
本科总预算（学费+生活费）：
意向专业（文商科/理工科/艺术类）：
</form_template>

Your task is to analyze the following user input and determine if they have filled out the form above:

<user_input>
{user_input}
</user_input>

Follow these steps to complete the task:

1. Carefully read through the user input.
2. Compare the information provided in the user input to the fields in the form template.
3. Note that the user doesn't need to fill out all fields for it to be considered filled. Even if they've only provided information for one or two fields, it still counts as filling out the form.
4. Look for any information that matches the fields in the form template, regardless of the order or exact wording.
5. If you find any information that corresponds to at least one field in the form template, consider the form as filled.
6. If you cannot find any information that matches any of the fields in the form template, consider the form as not filled.

Provide your answer in the following format:

<reasoning>
Reasoning: [Simply explain your reasoning here]
</reasoning>

<answer>
[YES/NO] - The form [is/is not] filled.
</answer>

Remember, even if only one field is filled with SPECIFIC and CLEAR information, the answer should be YES. Only answer NO if absolutely no SPECIFIC information clearly matching any field in the form template is provided. Do not consider vague, conversational, or unrelated content as form data.`).format({
      user_input: query
    })
  }
}