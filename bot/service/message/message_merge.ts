import { MessageReplyService } from './message_reply'
import { Config } from '../../config/config'
import { getChatId } from '../../config/chat_id'
import { HumanTransfer, HumanTransferType } from '../moer/components/human_transfer/human_transfer'
import { ChatHistoryService } from '../moer/components/chat_history/chat_history'
import { ChatDB } from '../moer/database/chat'
import {
  IReceivedMessage,
  IReceivedMessageSource,
  IReceivedRecallMsg,
  IReceivedTextMsg,
  IReceivedVoiceMsg,
  IWecomReceivedMsgType
} from '../../lib/juzi/type'
import { JuziAPI } from '../../lib/juzi/api'
import { LRUCache } from 'lru-cache'
import { isPastUser } from '../moer/components/flow/helper/pastUser'
import logger from '../../model/logger/logger'
import XunfeiASR from '../../model/nls/xunfei'
import { ChatStateStore, ChatStatStoreManager, ContactNameStore } from '../moer/storage/chat_state_store'
import { DataService } from '../moer/getter/getData'
import { HandleEnergyTest } from '../moer/components/flow/schedule/task/handleEnergyTest'
import { TaskName } from '../moer/components/flow/schedule/type'
import { FreeSpiritOCR } from '../../model/ocr/ocr'
import { EventTracker, IEventType } from '../../model/logger/data_driven'
import { RedisCacheDB } from '../../model/redis/redis_cache'
import { HomeworkTemplate } from '../moer/components/flow/helper/homeworkTemplate'
import { AsyncLock } from '../../lib/lock/lock'
import { ObjectUtil } from '../../lib/object'
import { LLM } from '../../lib/ai/llm/LLM'
import { Queue, Worker } from 'bullmq'
import { UUID } from '../../lib/uuid/uuid'
import { DateHelper } from '../../lib/date/date'
import { sleep } from '../../lib/schedule/schedule'
import { catchError } from '../../lib/error/catchError'
import { SendWelcomeMessage } from '../moer/components/flow/schedule/task/sendWelcomeMessage'
import { ChatInterruptHandler } from '../moer/components/message/interrupt_handler'


/**
 * 用于管理全局消息队列，为每个客户设置单独的计时器，合并处理消息
 */
export class GlobalMessageHandlerService {
  private static readonly messageSet = new LRUCache<string, any>({ max: 3000 })
  private static messageQueueBullMQ: Queue | undefined
  private static messageQueueWorker: Worker | undefined

  private static getQueueName() {
    const accountId = Config.setting.wechatConfig?.id ?? UUID.v4()
    return `user-message-queue_${accountId}`
  }

  private static getMessageStoreName(userId: string) {
    const chatId = getChatId(userId)

    return `user-message-store_${chatId}`
  }

  private static getMessageQueue (): Queue {
    if (!this.messageQueueBullMQ) {
      this.messageQueueBullMQ = new Queue(this.getQueueName(), {
        connection: RedisCacheDB.getInstance()
      })
    }

    return this.messageQueueBullMQ as Queue
  }

  public static async addMessage (message: IReceivedMessage) {
    if (!message.imContactId) {
      return
    }

    // 忽略掉 CRM 消息
    if (message.imContactId === '****************') {
      return
    }

    if (!message.isSelf && message.imRoomId && message.imRoomId === Config.setting.wechatConfig?.classGroupId) {
      // 班级群消息
      await this.handleClassGroupMessage(message)
      return
    }

    if (message.messageId && this.messageSet.has(message.messageId)) {
      return
    }

    const isSendByOpenAPI = message.source === IReceivedMessageSource.APIMessage
    if (isSendByOpenAPI) {
      // 忽略当前 AI 发送的消息
      return
    }

    // 忽略系统通知
    if ([IWecomReceivedMsgType.SystemMessage, IWecomReceivedMsgType.WeChatWorkSystemMessage].includes(message.messageType)) {
      return
    }

    // 企业内部员工发送的不进行处理
    if (message.coworker && !message.isSelf) {
      return
    }

    // 不处理群消息
    if (message.roomWecomChatId || message.imRoomId) {
      // logger.debug('忽略群消息', message.imRoomId)
      return
    }

    if (message.payload && 'text' in message.payload && message.payload.text) {
      logger.debug('接收消息', message.payload.text, message.imContactId)
      // 文本消息，提前 increment 一下 version 来即时打断
      await ChatInterruptHandler.incrementChatVersion(getChatId(message.imContactId))
    }

    if (message.messageId) {
      this.messageSet.set(message.messageId, 1)
    }

    const senderId = message.imContactId
    const messageId = message.messageId
    if (!messageId) {
      return
    }

    try {
      const messageStore = new RedisCacheDB(this.getMessageStoreName(senderId)) // 改为 message_store + chatId 来存储，否则可能有 key 冲突问题
      // 将消息存到 Redis
      await messageStore.addSet(JSON.stringify(message))

      let delay =  Config.setting.waitingTime.messageMerge
      if (Config.isTestAccount()) {
        delay = 2 * 1000
      } else if (message.isSelf) {
        delay = 0
      }

      // 添加消息，到消息队列
      await this.getMessageQueue().add(senderId, { userId: senderId, messageId }, { delay, removeOnComplete: true, removeOnFail: true })
    } catch (e) {
      logger.error('消息添加到任务队列失败', e)
    }
  }

  public static startWorker() {
    if (!this.messageQueueWorker) {
      this.messageQueueWorker = new Worker(this.getQueueName(), async (job) => {
        const { userId, messageId } = job.data

        // 只处理最新的消息
        const isLatest = await this.isLatestMessage(userId, messageId)
        if (!isLatest) {
          logger.debug(`跳过非最新消息: ${messageId}`)
          return
        }

        // 如果是 账号主动发送的消息，不做校验，直接处理
        if (userId !== Config.setting.wechatConfig?.id) {
          // 如果最后一条消息 是 AI 消息，并跟现在的时间比较接近，则做下延迟处理，拖延一下回复速度
          const messages =  await ChatHistoryService.getChatHistoryByChatId(getChatId(userId))
          if (messages.length > 0) {
            const lastAIMessageReplyDiff = DateHelper.diff(messages[messages.length - 1].created_at, new Date(), 'second')
            if (lastAIMessageReplyDiff < 5) {
              await sleep(5 * 1000)
            }
          }
        }

        GlobalMessageHandlerService.processUserMessages(userId)
      }, { connection: RedisCacheDB.getInstance(), concurrency: 20 })

      // 监听底层运行时报错 —— 一旦触发，就说明 Worker 已经“挂掉”了
      this.messageQueueWorker.on('error', (err) => {
        console.error('消息队列 Worker 发生未捕获错误', err)
      })
    }
  }

  private static async isLatestMessage(userId: string, messageId: string) {
    const messageStore = new RedisCacheDB(this.getMessageStoreName(userId))
    const messages = await messageStore.getSetMembers()

    if (!messages || messages.length === 0) {
      return false
    }

    // 消息有可能不是按顺序接收的，需要按时间重排序下
    messages.sort((msgA, msgB) => {
      return msgA.timestamp - msgB.timestamp
    })

    return messages[messages.length - 1].messageId === messageId
  }

  public static async processUserMessages (userId: string) {
    try {
      // 获取该客户在这段时间内发送的所有消息
      const messageStore = new RedisCacheDB(this.getMessageStoreName(userId))
      const userMessages = await messageStore.getSetMembers()

      // 消息有可能不是按顺序接收的，需要按时间重排序下
      userMessages.sort((msgA, msgB) => {
        return msgA.timestamp - msgB.timestamp
      })

      // 从消息队列中移除已处理的消息
      await messageStore.del()

      // 将消息转为文本
      const texts = await this.getTextsFromMessages (userMessages, userId)

      await this.replyMessage(userId, texts)
    } catch (e) {
      console.error (`处理客户 ${userId} 的消息出错：`, e)
    }
  }

  private static async getTextsFromMessages(messages: IReceivedMessage[], userId: string): Promise<string[]> {
    const texts : string[] = []
    const ignoreMessage = false // 是否忽略当前消息
    for (const message of messages) {
      try {
        if (message.messageId) {
          const cache = new RedisCacheDB(message.messageId)
          await cache.set(message, 3 * 60 * 60) // 缓存 3小时
        }
      } catch (e) {
        logger.warn('缓存消息失败')
      }


      try {
        let text = ''
        const isSendByOpenAPI = message.source === IReceivedMessageSource.APIMessage
        if (isSendByOpenAPI) {
          // 忽略当前 AI 发送的消息
          continue
        }

        // 忽略系统通知
        if ([IWecomReceivedMsgType.SystemMessage, IWecomReceivedMsgType.WeChatWorkSystemMessage].includes(message.messageType)) {
          continue
        }

        if (message.coworker && !message.isSelf) { // 企业内员工发送的不进行处理
          break
        }

        // 记录下聊天记录转发
        if (Config.isTestAccount() && message.messageType === IWecomReceivedMsgType.ChatHistory) {
          // await recordChatHistory(message)
          break
        }

        // // 只处理启动后的文本消息
        // if (message.timestamp < Config.setting.startTime) {
        //   console.log('忽略历史消息')
        //   continue
        // }
        const chatId = getChatId(userId)

        // 忽略欢迎语
        if (message.messageType === IWecomReceivedMsgType.Text && this.isWelcomeMessage((message.payload as IReceivedTextMsg).text as string) && await this.isFirstMessage(userId)) {
          await ChatStatStoreManager.initState(chatId)

          const isFriendAccepted = ChatStateStore.getFlags(chatId).is_friend_accepted
          if (isFriendAccepted) {
            continue
          }

          ChatStateStore.update(chatId, {
            state: {
              is_friend_accepted: true
            }
          })

          await new SendWelcomeMessage().process({
            userId: userId,
            chatId: getChatId(userId),
            name: TaskName.SendWelcomeMessage
          })

          await DataService.saveChat(getChatId(userId), userId)
          continue
        }

        switch (message.messageType) {
          case IWecomReceivedMsgType.Text: {
            const payload = message.payload as IReceivedTextMsg
            text = payload.text
            if (payload.quoteMessage) {
              if (!payload.quoteMessage.content.text) { // 引用非文本，查一下 对应的附件的注释
                // 查询一下 messageId 对应的 chatHistory
                const chatHistory = await ChatHistoryService.getMessageByMessageId(payload.quoteMessage.messageId)

                if (chatHistory && chatHistory.short_description) {
                  text =  `对“${chatHistory.short_description}”的回复是：\n ${payload.text}`
                } else {
                  text =  `${payload.text}`
                }
              } else {
                text =  `对“${payload.quoteMessage.content.text}”的回复是：\n ${payload.text}`
              }
            }
            break
          }

          case IWecomReceivedMsgType.Emoticon:
            text = '【表情】'
            break

          case IWecomReceivedMsgType.Voice: {
            // 语音转文字
            const msg = message.payload as IReceivedVoiceMsg
            if (msg.text) {
              text = msg.text
            } else {
              const xunfei = new XunfeiASR({
                appId: Config.setting.xunfeiASR.appId,
                secretKey: Config.setting.xunfeiASR.secretKey,
                uploadFileUrl: msg.voiceUrl
              })

              text = await xunfei.getResult()
            }

            break }

          case IWecomReceivedMsgType.MessageRecall: {
            // 埋点，数据库标记
            if (message.isSelf) {
              const userId = await JuziAPI.externalIdToWxId(message.customerExternalUserId as string)
              const chatId = getChatId(userId as string)
              const payload = message.payload as (IReceivedRecallMsg | undefined)
              if (payload) {
                const messageId = payload.content
                const cache = new RedisCacheDB(messageId)
                const originalMessage = await cache.get()

                const message = originalMessage?.payload?.text
                if (message !== undefined) {
                  EventTracker.track(chatId, IEventType.ManualReply, { type: '撤回', msg: message })
                }

                await catchError(ChatHistoryService.setMessageRecalled(messageId)) // 更新 聊天记录中的消息撤回标签
              }
            }
            break
          }

          case IWecomReceivedMsgType.Unknown:
          case IWecomReceivedMsgType.VoiceOrVideoCall:
            text = '【语音/视频通话】'
            break

          case IWecomReceivedMsgType.Image:
            text = await this.handleImageMessage(userId, getChatId(userId), message) ?? text
            break

          default:
            logger.log(JSON.stringify(message, null, 4))
            await this.handleUnknownMessageType(message)
        }

        // 客服或者手机端客服侧人工回复的消息，不处理，但是需要存一下
        if (message.isSelf && message.source !== undefined && [IReceivedMessageSource.MobilePush, IReceivedMessageSource.TeamConsoleManual].includes(message.source)) {
          logger.log('人工回复', JSON.stringify(message, null, 4))
          const userId = await JuziAPI.externalIdToWxId(message.customerExternalUserId as string)
          const chatId = getChatId(userId as string)

          // 添加埋点，客户 + AI 回复
          if (text) {
            const lock  = new AsyncLock()
            await lock.acquire(chatId, async () => {
              // 加一下锁，有可能被 其他流程 锁卡住, 这里等待下其他锁释放
              await ChatHistoryService.addBotMessage(chatId, text, undefined, { is_send_by_human: true, message_id: message.messageId })
              EventTracker.track(chatId, IEventType.ManualReply, { reply: text, message: message.source })
            }, { timeout: 5 * 60 * 1000 })
          }
        } else {
          texts.push(text)
        }

        if (texts.join('').length > 0) {
          await this.updateContactName(message, userId, chatId)
        }

      } catch (e) {
        // 避免消息处理过程中出错导致程序崩溃
        console.error('单条消息解析出错：', e)
      }
    }

    if (ignoreMessage) {
      return []
    }

    return texts
  }

  public static async handleUnknownMessageType(message: IReceivedMessage) {
    if (!message.imContactId)
      return

    const chat_id = getChatId(message.imContactId)
    logger.log(chat_id, '非文本消息类型', message.messageType)

    if (await ChatDB.isHumanInvolvement(chat_id)) { // 已经人工参与了，不再处理
      return
    }

    if (await isPastUser(message.imContactId)) {
      return
    }

    // 处理图片
    await HumanTransfer.transfer(chat_id, message.imContactId, HumanTransferType.UnknownMessageType, 'onlyNotify')

    EventTracker.track(chat_id, IEventType.TransferToManual, { reason: ObjectUtil.enumValueToKey(HumanTransferType, HumanTransferType.UnknownMessageType),
      message: JSON.stringify(message), msg_type: ObjectUtil.enumValueToKey(IWecomReceivedMsgType, message.messageType) })
  }

  private static isWelcomeMessage(msg: string) {
    if (msg.includes('我通过了你的联系人验证请求，现在我们可以开始聊天了') || msg.includes('我已经添加了你，现在我们可以开始聊天了。') || msg.includes('请求添加你为朋友') || msg.includes('我通过了你的朋友验证请求，现在我们可以开始聊天了'))
      return true

    const welcomeMsgRegex = /^我是.{1,10}$/
    return welcomeMsgRegex.test(msg)
  }

  private static async isFirstMessage(userId: string) {
    const chatId = getChatId(userId)

    return await ChatHistoryService.getUserMessageCount(chatId) === 0
  }

  private static async replyMessage(userId: string, texts: string[]) {
    try {
      return await MessageReplyService.reply(texts, userId)
    } catch (e) {
      // ignore
      logger.error(e)
    }
  }

  private static async handleClassGroupMessage(message: IReceivedMessage) {
    // 班级群消息
    // 先只识别文本消息判断是否为 作业模版
    const userId = message.imContactId as string
    const chatId = getChatId(userId)

    if (userId === Config.setting.wechatConfig?.id) {
      return
    }

    if (message.messageType === IWecomReceivedMsgType.Text) {
      const text = (message.payload as IReceivedTextMsg).text

      try {
        await HomeworkTemplate.handleHomework(text, userId, chatId)
      } catch (e) {
        logger.error({ cha_id: chatId }, '群作业打卡处理失败', e)
      }
    }
  }

  private static async handleImageMessage(userId: string, chatId: string, message: IReceivedMessage) {
    if (message.isSelf || message.coworker || userId.startsWith('1688')) { // 自己发送的图片或 墨尔员工发送的都不处理
      return
    }

    await ChatStatStoreManager.initState(chatId)

    const originalImageResponse = await JuziAPI.getOriginalImage(message.chatId, message.messageId as string)
    if (originalImageResponse.code !== 0) {
      return await this.handleUnknownMessageType(message)
    }

    const originalImageUrl = originalImageResponse.data.url

    const [error, imageOcrResult] =  await catchError(FreeSpiritOCR.recognizeUrl(originalImageUrl))

    if (!error && imageOcrResult && imageOcrResult.map((item) => item.text).join(' ').includes('能量频率层级测评')) {
      const text = imageOcrResult.map((item) => item.text).join(' ')

      // 如果能直接获取到分数，直接按照图片内的分数解读
      const score = this.extractScore(text)
      if (score) {
        ChatStateStore.update(chatId, {
          userSlots: {
            energy_test_score: score,
          },
        })
      }

      // Lock
      const lock = new AsyncLock()

      await lock.acquire(chatId, async () => {
        await new HandleEnergyTest().process({
          name: TaskName.CompleteEnergyTest,
          chatId: chatId,
          userId: userId,
        })

        await DataService.saveChat(chatId, userId)
      }, { timeout: 3 * 60 * 1000 })
    } else {
      const response = await new LLM({
        maxTokens: 200
      }).imageChat(originalImageUrl, `你作为一名墨尔冥想的老师在与客户的聊天过程中，客户发来一张图片，请对这张图片进行简要解释，要求言简意赅，只输出一段话。
- 如果有冥想相关的内容，请附加说明
- 【墨尔冥想】是你们公司的一款在线课程APP
- 如果是一场线上冥想直播课程的截图，就是唐宁老师在上课
- 如果图片显示“购买课程”或“购买课程后可观看”，则直接输出“由于登录没有使用购买手机号，显示购买课程后可观看”，反之则不要输出这条`)

      // 处理图片
      await HumanTransfer.transfer(chatId, userId, HumanTransferType.ProcessImage, 'onlyNotify', response)
      EventTracker.track(chatId, IEventType.TransferToManual, { reason: ObjectUtil.enumValueToKey(HumanTransferType, HumanTransferType.UnknownMessageType),
        image_url: originalImageUrl, msg_type: ObjectUtil.enumValueToKey(IWecomReceivedMsgType, message.messageType) })

      return `【图片】${response}`
    }
  }

  private static extractScore(text: string) {
    try {
      const pattern =  /(-?\d+)分\/\d+分/
      const match = text.match(pattern)

      if (!match) return null

      let score = match[1]
      if (score.length > 3) {
        score = score.slice(-3)
      }

      const numberScore = Number(score)

      // 多重验证
      if (
        !Number.isFinite(numberScore) || // 检查是否为有限数字
        !Number.isInteger(numberScore) // 检查是否为整数
      ) {
        return null
      }

      return numberScore
    } catch (error) {
      return null
    }
  }


  private static async updateContactName(message: IReceivedMessage, userId: string, chatId: string) {
    try {
      await ChatStatStoreManager.initState(chatId)
      if (ContactNameStore.getContactName(chatId) && message.contactName && ContactNameStore.getContactName(chatId) !== message.contactName) {

        logger.log(`更新联系人名称 ${chatId} ${ContactNameStore.getContactName(chatId)} -> ${message.contactName}`)
        await ChatDB.updateContact(chatId, userId, message.contactName)
      }
    } catch (e) {
      logger.error('更新联系人姓名失败', e)
    }
  }
}