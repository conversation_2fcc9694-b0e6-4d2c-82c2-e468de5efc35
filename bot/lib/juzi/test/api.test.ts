import { <PERSON>ziAP<PERSON> } from '../api'
import { IWecomMsgType } from '../type'
import { Config } from '../../../config/config'
import { MessageSender } from '../../../service/moer/components/message/message_send'
import { GroupNotification } from '../../../service/moer/notification/group'
import { DataService } from '../../../service/moer/getter/getData'
import { ClassGroupSend } from '../../../service/moer/components/flow/schedule/task/classGroupSend'
import { ClassTaskName } from '../../../service/moer/components/flow/schedule/type'
import {
  ClassGroupPreCourseSundayScript
} from '../../../service/moer/components/script/pre_course_day_class_group_script'
import { loadConfigByAccountName } from '../../../../test/tools/load_config'
import { IChattingState } from '../../../service/moer/storage/chat_state_store'
import { PrismaMongoClient } from '../../../model/mongodb/prisma'
import axios from 'axios'
import { ClassGroupTaskManager } from '../../../../bot_starter/client/class_group'
import { Queue } from 'bullmq'
import { RedisDB } from '../../../model/redis/redis'

describe('Test', function () {
  beforeAll(() => {
    Config.setting.wechatConfig =   {
      id: '****************',
      botUserId: 'ShengYueQing',
      name: 'QiaoQiao',
      notifyGroupId: 'R:*****************',
      classGroupId: 'xx',
      courseNo: 1
    }
  })

  it('获取客户信息', async () => {
    // ****************_****************
    const customerInfo = await JuziAPI.getCustomerInfo('****************', '****************')

    if (customerInfo) {
      console.log(customerInfo.imInfo.externalUserId)
    }
  }, 60000)

  it('改群名', async () => {
    // 获取群信息：await JuziAPI.listGroup(id)

    await JuziAPI.changeRoomName({
      imRoomId: 'R:*****************',
      imBotId: '****************',
      name: 'QiaoQiao'
    })
  }, 60000)

  // it('externalId', async () => {
  //   // Config.setting.wechatConfig.id =  '1688857949631398'
  //   console.log(JSON.stringify(await JuziAPI.wxIdToExternalUserId('1688857949631398')), null, 4))
  // }, 60000)

  // it('获取聊天记录', async () => {
  //   console.log(JSON.stringify(await JuziAPI.pullChatHistory('2024-04-15', '10', '1'), null, 4))
  // })

  it('拉群测试', async () => {
    // const res = await JuziAPI.addToGroup({
    //   botUserId: 'fk',
    //   contactWxid: 'u',
    //   roomWxid: 'bitch'
    // })
    //
    // console.log(JSON.stringify(res.data, null, 4))
  }, 30000)

  it('获取客户列表', async () => {
    console.log(JSON.stringify(await JuziAPI.listCustomers('1688855025632783'), null, 4))
  }, 30000)

  // it('发送消息', async () => {
  //   console.log(JSON.stringify(await JuziAPI.sendMsg('****************', 'Ming',  {
  //     type: IWecomMsgType.Emoticon,
  //     imageUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/0516bf9c808dce3297b345f1d93150d5.jpg'
  //   }), null, 4))
  // }, 30000)
  //
  // it('发送消息2', async () => {
  //   console.log(JSON.stringify(await JuziAPI.sendMsg('****************', 'Ming',  {
  //     type: IWecomMsgType.Text,
  //     text: '测试消息'
  //   }), null, 4))
  // }, 30000)

  it('添加好友测试', async () => {
    console.log(JSON.stringify(await JuziAPI.addFriendByPhone({
      imBotId: '****************',
      phone: '***********',
      hello: '墨尔冥想的助教'
    }), null, 4))
  }, 60000)

  it('*********', async () => {
    const config = await PrismaMongoClient.getConfigInstance().config.findFirst(
      {
        where: {
          enterpriseName: 'moer',
          accountName: 'tongtong'
        }
      }
    )

    if (!config) return

    await axios.post(`${config.address  }/clear_cache`, { chatId: '7881299870923686_1688857949631398' })

  }, 30000)

  it('查看群聊', async () => {
    // console.log(JSON.stringify(await JuziAPI.listGroup('****************'), null, 4))

    const groups =  await JuziAPI.listGroup('****************')

    for (const group of groups) {
      console.log(group.name, group.imRoomId)
    }

  }, 30000)

  it('添加 123123', async () => {
    // 添加任务然后再清空一下
    Config.setting.wechatConfig = await loadConfigByAccountName('moer7')

    const groupIds = Config.setting.wechatConfig.classGroupIds as string[]

    await ClassGroupTaskManager.createTasksForGroup(groupIds)
  }, 1E8)


  it('查看群任务', async () => {
    // 添加任务然后再清空一下
    Config.setting.wechatConfig = await loadConfigByAccountName('moer7')

    const queueName = ClassGroupTaskManager.getQueueName()

    const queue = new Queue(queueName, {
      connection: RedisDB.getInstance()
    })

    const jobs = await queue.getJobs()

    console.log(JSON.stringify(jobs, null, 4))
  }, 1E8)

  it('将好友踢出群聊', async () => {
    const contactWxid = Config.setting.wechatConfig?.id as string

    const imRoomId = 'R:*****************'
    await JuziAPI.kickNonWhitelistMembers(contactWxid, imRoomId)
  }, 60000)

  it('发送群消息', async () => {
    await GroupNotification.notify('测试群消息')
  }, 60000)

  it('externalId To UserId', async () => {
    console.log(JSON.stringify(await JuziAPI.externalIdToWxId('wm41XZJQAAraXYGQlIcWplXS58GTqrew'), null, 4))
  }, 30000)

  it('queryCustomerInfo', async () => {
    console.log(JSON.stringify(await JuziAPI.getCustomerInfo('****************', '****************'), null, 4))
  }, 30000)

  it('wxId to externalId', async () => {
    console.log(JSON.stringify(await JuziAPI.getCustomerInfo('1688857949631398', '7881301324929786'), null, 4))

  }, 30000)

  it('发送群公告', async () => {
    Config.setting.localTest = false
    await MessageSender.sendById({
      // user_id: '7881300846030208',
      chat_id: '7881300846030208_1688857003605938',
      room_id: 'R:10704146672803624',
      ai_msg: ClassGroupPreCourseSundayScript.group_announcement(),
    }, { isAnnouncement: true })
  }, 60000)

  it('改群名', async () => {
    // 获取群信息：await JuziAPI.listGroup(id)
    const room_id = 'R:10704146672803624'
    // const bot_id = '****************'
    const bot_id = Config.setting.wechatConfig?.id as string
    console.log(bot_id)

    await JuziAPI.changeRoomName({
      imRoomId: room_id,
      imBotId: bot_id,
      name: '没想好'
    })
  }, 60000)

  it('输出群名', async () => {
    const bot_id = Config.setting.wechatConfig?.id as string
    const room_id = 'R:10704146672803624'
    const all_groups = await JuziAPI.listGroup(bot_id)
    console.log(all_groups.find((group: { imRoomId: string }) => group.imRoomId === room_id).name)
  }, 60000)

  it('同步群聊', async () => {
    const bot_id = Config.setting.wechatConfig?.id as string
    const response = await JuziAPI.groupSync(bot_id)
    console.log(response)
  }, 60000)

  it('group_send_test', async () => {
    Config.setting.localTest = false
    await new ClassGroupSend().process(({
      chatId: '7881300846030208_1688857003605938',  // UUID.short()
      userId: 'R:10704146672803624',  // Horus：7881302298050442
      name: ClassTaskName.AfterClassMeeting4Day3,
      scheduleTime: {
        is_course_week: false,
        day: 5,
        time: '21:00:00'
      }
    }))
  }, 600000)

  it('测试语音', async () => {
    Config.setting.localTest = false
    await MessageSender.sendById({
      // user_id: '7881300846030208',
      chat_id: '7881300846030208_1688857003605938',
      ai_msg: 'test',
      room_id: 'R:10704146672803624'
      // send_msg: {
      //   type: IWecomMsgType.Link,
      //   sourceUrl: 'https://www.wolai.com/cE7SEBXPFNghB2WJmw97kZ',
      //   title: '小讲堂完课奖励',
      //   summary: '点击进入查看',
      //   imageUrl: 'https://workmate.s3.cn-northwest-1.amazonaws.com.cn/link_msg/6b32c09b-915a-4347-b090-e5955fad71fb/fbd3e9ea-6a22-4546-b895-061cf6312c96.jpg'
      // }
      // send_msg: {
      //   type: IWecomMsgType.MiniProgram,
      //   appId:  'gh_7e79cc9a6e81@app',
      //   description:  '5天冥想入门营｜正念调频',
      //   pagePath:   'pages/course/play.html?sku=20241030007040',
      //   thumbUrl:   'https://workmate.s3.cn-northwest-1.amazonaws.com.cn/link_msg/a0afaf91-2e11-425b-a5b3-322d92ada927/fd0a950a-92e3-4307-8e64-6baefc16413c.jpg',
      //   title:  '墨尔冥想',
      //   username:   'wxb944dac25d627482',
      //   iconUrl:   'http://mmbiz.qpic.cn/mmbiz_png/ELJnCVnUFFicdsWiaU24SEXic4zzgXAEJIR8yMwEpoeNZibulErNbicIiaibzVQkApzQt77uZyWia5xic4ZaD4Jt3P4EoHA/640?wx_fmt=png&wxfrom=200'
      // }
    }, { isAnnouncement: true })

  }, 30000)

  it('', async () => {
    console.log(await DataService.hasPurchasedIntroCourseBefore('1051783'))
  }, 60000)

  it('测试', async () => {
    Config.setting.localTest = false

    // await WechatMessageSender.sendById({
    //   chat_id: '',
    //   user_id: '7881300846030208',
    //   ai_msg: 'asdas',
    //   send_msg:  {
    //     type: IWecomMsgType.VideoChannel,
    //     'avatarUrl': 'http://wx.qlogo.cn/finderhead/DxncLlErbRFfH8SzUPHHyH6B4CibBoRkxpoFGmawibSm8t4aYgmV5MmpGfhick2DSlrmcdFGfyfxLk/0',
    //     'coverUrl':  'http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiaPsOloD6UoLYyfG60PwcaKKWPe8zZxVW5luoY0HBsgqZuTLJbsD7HUaC9IQ6WV4QA5o5z1oHACdRXibPKic6J4Ddg&token=cztXnd9GyrG3CzaUYiaaH9ZJ3MmcnibhWKpLYgsR8Ev1uJVHf6RRM3WKuacibiafn2OcMLpX22nRhgQyr2XgL7kllagqBJQxgGNicQhibSdYhrBsTkAjCnxvbqBMBkjvP308dY&idx=1&dotrans=0&hy=SZ&m=&scene=2&uzid=2&finder_expire_time=1730191119&finder_eid=export%2FUzFfAgtgekIEAQAAAAAAbpwD-aksVgAAAAstQy6ubaLX4KHWvLEZgBPEz6NMKiJ3FsmKzNPgMIIQ-zZ02PKj0FYI0iKKQ-8T',
    //     'description': '建立丰盛显化意识，实现财富增长，今晚八点不要错过老师的课程哦~',
    //     'extras': '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',
    //     'feedType': 4,
    //     'nickname': '墨尔冥想-入门营',
    //     'thumbUrl': 'http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiaPsOloD6UoLYyfG60PwcaKKWPe8zZxVW5luoY0HBsgqZuTLJbsD7HUaC9IQ6WV4QA5o5z1oHACdRXibPKic6J4Ddg&token=cztXnd9GyrG3CzaUYiaaH9ZJ3MmcnibhWKpLYgsR8Ev1uJVHf6RRM3WKuacibiafn2OcMLpX22nRhgQyr2XgL7kllagqBJQxgGNicQhibSdYhrBsTkAjCnxvbqBMBkjvP308dY&idx=1&dotrans=0&hy=SZ&m=&scene=2&uzid=2&finder_expire_time=1730191119&finder_eid=export%2FUzFfAgtgekIEAQAAAAAAbpwD-aksVgAAAAstQy6ubaLX4KHWvLEZgBPEz6NMKiJ3FsmKzNPgMIIQ-zZ02PKj0FYI0iKKQ-8T',
    //     'url': 'http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiaPsOloD6UoLYyfG60PwcaKKWPe8zZxVW5luoY0HBsgqZuTLJbsD7HUaC9IQ6WV4QA5o5z1oHACdRXibPKic6J4Ddg&token=cztXnd9GyrG3CzaUYiaaH9ZJ3MmcnibhWKpLYgsR8Ev1uJVHf6RRM3WKuacibiafn2OcMLpX22nRhgQyr2XgL7kllagqBJQxgGNicQhibSdYhrBsTkAjCnxvbqBMBkjvP308dY&idx=1&dotrans=0&hy=SZ&m=&scene=2&uzid=2&finder_expire_time=1730191119&finder_eid=export%2FUzFfAgtgekIEAQAAAAAAbpwD-aksVgAAAAstQy6ubaLX4KHWvLEZgBPEz6NMKiJ3FsmKzNPgMIIQ-zZ02PKj0FYI0iKKQ-8T'
    //   }
    // })
    //
    // await WechatMessageSender.sendById({
    //   chat_id: '',
    //   user_id: '7881300846030208',
    //   ai_msg: 'asdas',
    //   send_msg:   {
    //     type: IWecomMsgType.VideoChannel,
    //     avatarUrl: 'http://wx.qlogo.cn/finderhead/DxncLlErbRFfH8SzUPHHyH6B4CibBoRkxpoFGmawibSm8t4aYgmV5MmpGfhick2DSlrmcdFGfyfxLk/0',
    //     coverUrl: 'http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiaeaZZQtHYsqxzwgWRybrVopNib077e9h0mkFS13JJX8WBKpDUfKZ0TxcapuHmmMS3ss35GtyeNvKaweMorOH6YTg&token=6xykWLEnztJALj7BIfsmA9ARibE8o2q37mCCcRT9QI7vunO1chQQbHp5wBLEHkJqYTvxicggD5VerH4fO5aL7uTntAuiaGb2PGY2RBY3NMGXvhygnqyBaF7Vic9SqGgvdVsw&idx=1&dotrans=0&hy=SZ&m=&scene=2&uzid=2&finder_expire_time=1730975310&finder_eid=export%2FUzFfAgtgekIEAQAAAAAA6usvDWcBaAAAAAstQy6ubaLX4KHWvLEZgBPE1qMEJw50XtaKzNPgMIKl2fWTadXW83cxTAiJSDnA',
    //     description: '欢迎大家加入墨尔大家庭，一起学习成长~',
    //     extras: 'CAEQACLEFwAE9OmXBAAAAQAAAAAAv5SW5cWRhSPW685bI2cgAAAAaeq5SzX7s7sPwaz04zCEwYwyALHFYGIb/l1etP1AtP1JcPIDtseKLS3OztVbYCMbpO0H6z59Y0xKm6e9VfNmfnzfLEd4ApWzVjdKIQW8wRl4ywCdIKb/GUxc2zTLnKIdYgPq4EBzh8YqwfVuOS/QPlRrH0oSLTEDoGa75/jj3B/Wij+kSN1vIzf+41wPPRV7+mOoXZX8o0MOFkG3d6brvHAMhny0bzthYk3BZpb4udE9phSpbtoPETyffrJoe82mVM4lwRnfExpPncOoxZ6Yls6sJsN5CYc5mYBfJwq/OnMDr9WieAQvZt/XwvtE+ILgYjUZp1uzNTETrEw1ByUA3M0YYtYI48XX7xODgJdKq4VGA1FlRq1Mv8nocUnUbEPU/UMyOPNo8bgl/GMKk9VL/j9Lfylgo5v0WpqcTunSuRLnke8/hpViGuaFxej278r9IhIMJGw/tS8k87GepDzg7Emp6Yll8K44CEliF4ndhAhQHSH0RS7P/+Jnuva9g7SV0MTBVeT302Sp5roHhcUZbqMcXSN/rMneyi/n9DQ8q/T5xAqNO7RsnX7FiD5uvm6E6qqPa/SOQeWayiLYSPm2fYNZRf7U5bdJURCYTFedInuegfI9COoOfQFrCM6jZ4WlqvWEn3CaL2ZLBKAKnxv/bp2XCimVWv/UH9aBPOAk8RnfFxlpS7jCI2A7hN88PYP5SgJ0LeCRQ6+dlGfnwv0zfN9dwZDTCyfOdNhPXPz9yMwCGgxiIzcLmRrwPpJHXcpf54CP/ZIOS6r64KPNUtGxjWKZRKI9I0h4k0CPLOGrFzj9S4n4tYOjJBvk9xVbZl71I+9PPR6HpHJQZjpDvcOLTwFh4KxfhvWgAOWi23SmgeJqKHYpBOTvt+fc9FPEDI2QXgm4dG8NltsattkTKtjgjFUeZ6lRO1zNOMSb7dHH5di7UYjeoqcGKypSIauFZCLPf1euIV2LvNqkDQ70hyoclB+H7Xc4XSyAYADybQIC2Ej7mtoO0p7SFqv2yjxNWXBvSZ5KIQj1Nk9bzH0sbPOI8hSu8oW2y9dwHAXz5VeTH4rpTn/Bt1unCZgCpNZNGXlbhF4tTLl8ryPvhyEsmPDpJUTCDvJPcrk/l5emsJ7ZBrTvwKJ6j8PQ7POESA1CUWI/PzX1peWKsEHsm9/4aVuNNhD7tR4SGqdu0Jp1NOyTZpvTyhp4ejCP3V/0Lcih22TpB5ZgeEbdhkV49uwLVjQ58z4+odWt1j/yxhUSbLxSGmGkyWvA7qQqn4L+Y3ylte3a5EZtVpaGT2vGBsIsHej9zVIhJZi+fDBfBNV1KftkmXtdlLtsF5SgmwqgAbIz1qzU30RvxmvC69orM8hCQTZscyKrj0nbdkBoiqGuYpDEmOHMXft4Sg2Y7FvYCtTI7Zw7B2cud1bl1tTk8DvK8jme/eEt8p+n34A5gy+VDBEObpZXIs+RhrCr5lxMyDcZYviTpvapR77Y598mg/2UHCo9zdS/81Gjqm10mrdFcOJUVN71CfBIHNK1RLOIxrPu5G7/lq4l/UWqNMuoWmdHqK6em3fsE4eWbGQYDFdt3CJi7XI/38AxuPeBR6WU6NAXbXoXtNjbJ5oli+E4BuATOcrubVFo+ITpWU/UBhKm0sthOi/Fkc8KSvSV9pp3vvDR3uNAT4X43lyOkpqyjK70C7pmPl/mmN2F7Ys2kWfkV+HLdRcjO6vy4sorNz1Q0Y+iePHsOSJODueYi7ZHXGxCmIUCZB88w+qW+Ms6vJnx5unTV3Qi+LEG/nmGlCI7GtiuQ9Zn62qlaJTIae7v8g3IF0dF7H6qSLxL+s7uLajx0R0NIO43QCtpMpet83GySdUV6JjC7oflEJBz6uBwTOkWgTq090ovSVUUQ45f9NWhrvrla7dSAojmT0Agbo9GVXd3Nd4UGRVIwHx8ComVVoGHT9/yUQu5busOb69kUbGJSRljlJEsZfJVH3dfdVetV+4BrBpmMJJ2zESsnRa6RXcM6OCJJq5OzCOkqElfMOocftHbkD3lTKaZQbEhJpQiTHgssFulagzz0CrE1C5l3wc5yuZ4LEeg93WQ1zXT9F47suGXKalBiWoL/TmjoiglxA+fiINE8NM2OMS6DqPyyYmSm4YGmrzUbAqDZ2DaBL7TbNUqn8x9ww9Z/NICOWLZ7k3SpTSqMb2w7jUNRa2TuuD52hPbcYU8u2dxzYF68Nr3NdQXjieFi8ImfvHgYRBnpSbE9/fI5STsf+3ot9VnBz1Qb4hES8ugSIMPIfxHv7oxdZ+vQmHXzP1eFYVIyFPdwuyQlHH4TNuiB9SIU3bJJHM0toVEWtQqrKXH1NdhkuUpO2hxcCEK00JMvtCsFFV4eHYFUVbp5yX/uS/C0yFn/UmEs7U4sRDRWDvVP/bYMpu6qRD0PiQmWls1bL1g9pj7daYkYPtN2Anr+NRy8qOWb5AYLcfub/HUJVUMskm/jlS6fWikFn6/3oZRlBz2kKjR6BX/J1r0qJBEf88FGvbsVaafOSld261ipFUE1TYDPUDMNDuWgNPahjIIoRPHVl3F1lOnwhWnZA5D6QCKaGPiOFbZGEjnXqnApQ/Z//BeN6IvtEduHWOrlPhCISn1+STcXuYkGnZRwYLkg8Ebkjn7AmXBKHRo7nBjXR/JlTChjrQJNPKcEh9ekrYOnZWdaWvf9Ejn+aVTCXYLIB6MewgrZW/IZeSgotsoc7Jptrx5J/FtJ0hzR7+VXBh2fdIbBumeqLDuAfW4Cs1w4z/Gs6A+1MatJWm4qMHHJwKh6LepBDvOPc1qUIaJm4mu+QloibSyVNKuOWWjNCzMt0z9qwkwSdgKmawtUaNizHh07ufCvDIO/H+ZRyhn1/JE+Wa/dBN5z+WXIaOiSLJCe9lxJX9uSDkilYm6v7N8UW7k3yG+n00qC5eMV/mR/oVxQauhpN6VzxH20F5MqD3t6jGkhAxFTBsmRD3QX/Pci/zQPQAZCHuwoYZplJ0lH7FHXtf0h52yBicPUevZA0q/aQ0RohRLMU5qPXHIN9YS2M0bOBgmw7mkEpoZFERMFM9ob2PHfonnmfnqNlIyyUXrwNVlbifXhCZSQkAN9ZwJ8WXFJo7BCf/vJCsPkKIn4Agj8xm82EbBnSewCeRbPZ/xaed2d8hU/9258DbDGDYLzXQh1mmfS86EIOTLXSm7oHK2qZF6e823GcYLG+63NcWSgobinnunNwEiVXo77bwwSlBMCXeQsOee8hGevZqRXFv+cY6W7ufA7B0k/Lp8w+rSNp7Yg1ckJ6ryn/Y/8FfB3EbevUicok5lP+ZgaJHq/+UoRe8Cp3Irh6wI5DFsRbPj1BfnqjAs/s1+O59936hBzt//Yc9Y2glCQIEujSGMwx9bElWnTKUsin7hTnJS/pVSORF3Tkff819GtZ36e7fJNPNbH9PNOQWCDk8a0widnRbmzM1DliNhBLlPymtPRfryqYHFh4p1qBc+59qZznboib1st15+6UfUpTKobRQcgWMGV4UgoFSKqUteXcgLRQtvg/7GaHx9AdDsC064drgrY3vTTxqHFrElLF4RzTasKqiKNgAnRupu8RZSWgsC92h/qoAJ0hWAz62DVN+aQ8Sz+F6Kh20y7ViYjCR1kvgtKAXSEC4MkSVUCKYnxkmVtmE3Z9czvQ803uqOIIfuW5p0OTLmt75NSbZ9QfOkbnwH/CIQxcl67xJI/Sra6bA6yil09ihAoWgQs/Gryzl+//R1HfJOndFIrc4/jZZ9EkOGSfhEzQWk68EROJevusJHGbE3I01jF/fALyk3/W3+WmbPZLIZaNEE5+9CI2V1XW45TpAHIrQwPYUzyvbqeimD/XKgS2anLsS3/NToNfmu7JMdYFItmk23f8DM/EFbBxz8C9YdOZ8wWcCcWbUz5hr/bRLzMvSlA9k9JDZyl0odMoy29hOZZo+rXfVvgShVM6GrBJ8j4aloefi10FxNI5p3tft9wCgA',
    //     feedType: 4,
    //     nickname: '墨尔智慧',
    //     thumbUrl: 'http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiaeaZZQtHYsqxzwgWRybrVopNib077e9h0mkFS13JJX8WBKpDUfKZ0TxcapuHmmMS3ss35GtyeNvKaweMorOH6YTg&token=6xykWLEnztJALj7BIfsmA9ARibE8o2q37mCCcRT9QI7vunO1chQQbHp5wBLEHkJqYTvxicggD5VerH4fO5aL7uTntAuiaGb2PGY2RBY3NMGXvhygnqyBaF7Vic9SqGgvdVsw&idx=1&dotrans=0&hy=SZ&m=&scene=2&uzid=2&finder_expire_time=1730975310&finder_eid=export%2FUzFfAgtgekIEAQAAAAAA6usvDWcBaAAAAAstQy6ubaLX4KHWvLEZgBPE1qMEJw50XtaKzNPgMIKl2fWTadXW83cxTAiJSDnA',
    //     url: 'https://channels.weixin.qq.com/web/pages/feed?eid=export%2FUzFfAgtgekIEAQAAAAAA6usvDWcBaAAAAAstQy6ubaLX4KHWvLEZgBPE1qMEJw50XtaKzNPgMIKl2fWTadXW83cxTAiJSDnA'
    //   }
    // })

    await MessageSender.sendById({
      chat_id: '',
      user_id: '7881300846030208',
      ai_msg: 'asdas',
      send_msg: {
        type: IWecomMsgType.Link,
        summary: '每天45分钟，助你成事悦己，高频正觉',
        imageUrl: 'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/2421741338713_.pic.jpg',
        title: '21天|冥想系统班',
        sourceUrl: 'https://h5.esnewcollege.com/pages/course/detail?sku=20240107008805&pid=665301&checkLogin=1'
      }
    })
    //
    //
    // await WechatMessageSender.sendById({
    //   chat_id: '',
    //   user_id: '7881300846030208',
    //   ai_msg: 'asdas',
    //   send_msg:   {
    //     type: IWecomMsgType.VideoChannel,
    //     'avatarUrl': 'http://wx.qlogo.cn/finderhead/DxncLlErbRFfH8SzUPHHyH6B4CibBoRkxpoFGmawibSm8t4aYgmV5MmpGfhick2DSlrmcdFGfyfxLk/0',
    //     'coverUrl':  'http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiaPsOloD6UoLYyfG60PwcaKKWPe8zZxVW5luoY0HBsgqZuTLJbsD7HUaC9IQ6WV4QA5o5z1oHACdRXibPKic6J4Ddg&token=cztXnd9GyrG3CzaUYiaaH9ZJ3MmcnibhWKpLYgsR8Ev1uJVHf6RRM3WKuacibiafn2OcMLpX22nRhgQyr2XgL7kllagqBJQxgGNicQhibSdYhrBsTkAjCnxvbqBMBkjvP308dY&idx=1&dotrans=0&hy=SZ&m=&scene=2&uzid=2&finder_expire_time=1730191119&finder_eid=export%2FUzFfAgtgekIEAQAAAAAAbpwD-aksVgAAAAstQy6ubaLX4KHWvLEZgBPEz6NMKiJ3FsmKzNPgMIIQ-zZ02PKj0FYI0iKKQ-8T',
    //     'description': '建立丰盛显化意识，实现财富增长，今晚八点不要错过老师的课程哦~',
    //     'extras': '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',
    //     'feedType': 4,
    //     'nickname': '墨尔冥想-入门营',
    //     'thumbUrl': 'http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiaPsOloD6UoLYyfG60PwcaKKWPe8zZxVW5luoY0HBsgqZuTLJbsD7HUaC9IQ6WV4QA5o5z1oHACdRXibPKic6J4Ddg&token=cztXnd9GyrG3CzaUYiaaH9ZJ3MmcnibhWKpLYgsR8Ev1uJVHf6RRM3WKuacibiafn2OcMLpX22nRhgQyr2XgL7kllagqBJQxgGNicQhibSdYhrBsTkAjCnxvbqBMBkjvP308dY&idx=1&dotrans=0&hy=SZ&m=&scene=2&uzid=2&finder_expire_time=1730191119&finder_eid=export%2FUzFfAgtgekIEAQAAAAAAbpwD-aksVgAAAAstQy6ubaLX4KHWvLEZgBPEz6NMKiJ3FsmKzNPgMIIQ-zZ02PKj0FYI0iKKQ-8T',
    //     'url': 'http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiaPsOloD6UoLYyfG60PwcaKKWPe8zZxVW5luoY0HBsgqZuTLJbsD7HUaC9IQ6WV4QA5o5z1oHACdRXibPKic6J4Ddg&token=cztXnd9GyrG3CzaUYiaaH9ZJ3MmcnibhWKpLYgsR8Ev1uJVHf6RRM3WKuacibiafn2OcMLpX22nRhgQyr2XgL7kllagqBJQxgGNicQhibSdYhrBsTkAjCnxvbqBMBkjvP308dY&idx=1&dotrans=0&hy=SZ&m=&scene=2&uzid=2&finder_expire_time=1730191119&finder_eid=export%2FUzFfAgtgekIEAQAAAAAAbpwD-aksVgAAAAstQy6ubaLX4KHWvLEZgBPEz6NMKiJ3FsmKzNPgMIIQ-zZ02PKj0FYI0iKKQ-8T'
    //   }
    // })
    //
    //
    // await WechatMessageSender.sendById({
    //   chat_id: '',
    //   user_id: '7881300846030208',
    //   ai_msg: 'asdas',
    //   send_msg:   {
    //     type: IWecomMsgType.VideoChannel,
    //     avatarUrl: 'http://wx.qlogo.cn/finderhead/S4gvmAA4RuJJoCYucthibbriargqeWpymrIaRJMkGeYjkhuffBYJbIz2BCba7m3EREGcHsrqicdfuY/0',
    //     coverUrl: 'http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiaKrrAgAiaHDO0pCOCJ21YpBwBwiag5HcqsAaKDGr8miaaUPdiaq3xfmIwXkVk6JXlRo6VZU0vYM4zFgbAIDxmwic4NqQ&token=cztXnd9GyrHZSb7Xb38ib0cCc2nx0Zrfywyd7Jblsjh9JIOECB3dQictLvdaqVXPIcXusAqQRGOcQIZ2zBB2PPx8QEIS8UvsIt2OBWPRKIy6ia8jiaXxYjuedfibZRWNIpico2&idx=1&bizid=1023&dotrans=0&hy=SZ&m=&scene=0&finder_expire_time=1726727279&finder_eid=export%2FUzFfAgtgekIEAQAAAAAArywtSwTXtgAAAAstQy6ubaLX4KHWvLEZgBPEx4FsNWQvDPSEzNPgMILjy4wAFYOewM8qDjjZB6SB',
    //     description: '冥想它让你不再把目光去向外追逐，而是把你所有的专注焦点收摄回当下，收回到你的内在力量#冥想#修行#知识分享#詹唐宁',
    //     extras: '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',
    //     feedType: 4,
    //     nickname: '墨尔冥想',
    //     thumbUrl: 'http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiaKrrAgAiaHDO0pCOCJ21YpBwBwiag5HcqsAaKDGr8miaaUPdiaq3xfmIwXkVk6JXlRo6VZU0vYM4zFgbAIDxmwic4NqQ&token=cztXnd9GyrHZSb7Xb38ib0cCc2nx0Zrfywyd7Jblsjh9JIOECB3dQictLvdaqVXPIcXusAqQRGOcQIZ2zBB2PPx8QEIS8UvsIt2OBWPRKIy6ia8jiaXxYjuedfibZRWNIpico2&idx=1&bizid=1023&dotrans=0&hy=SZ&m=&scene=0&finder_expire_time=1726727279&finder_eid=export%2FUzFfAgtgekIEAQAAAAAArywtSwTXtgAAAAstQy6ubaLX4KHWvLEZgBPEx4FsNWQvDPSEzNPgMILjy4wAFYOewM8qDjjZB6SB',
    //     url: 'https://channels.weixin.qq.com/web/pages/feed?eid=export%2FUzFfAgtgekIEAQAAAAAArywtSwTXtgAAAAstQy6ubaLX4KHWvLEZgBPEx4FsNWQvDPSEzNPgMILjy4wAFYOewM8qDjjZB6SB'
    //   }
    // })
    //
    // await WechatMessageSender.sendById({
    //   chat_id: '',
    //   user_id: '7881300846030208',
    //   ai_msg: 'asdas',
    //   send_msg:  {
    //     type: IWecomMsgType.VideoChannel,
    //     'avatarUrl': 'http://wx.qlogo.cn/finderhead/S4gvmAA4RuJJoCYucthibbriargqeWpymrIaRJMkGeYjkhuffBYJbIz2BCba7m3EREGcHsrqicdfuY/0',
    //     'coverUrl': 'http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkia5a31eAjWic7aq6iaFanCDHdBwfA1lDss7vYlLeicoJRYmJWO8Q3jibGZasBvlj6WBlFkZxCHyp1fclLoC8XAXSvZicA&token=cztXnd9GyrEAKOSECretl8gga3LMEjDwVdv2iaKfx9NnUgFFNPc6f1NxUdgeSGq4uSicSp8ktic9QaECD7luyMhGmc4ricZ0jiaNsXia3ExrSePtsYpPJPl0ts0vIRWWZVgicQI&idx=1&bizid=1023&dotrans=0&hy=SZ&m=&scene=0&finder_expire_time=1728033170&finder_eid=export%2FUzFfAgtgekIEAQAAAAAAj4wSnYK1NQAAAAstQy6ubaLX4KHWvLEZgBPEzIFoFwIgANmFzNPgMIJBxowCIGVsfp8NDeTdPrxy',
    //     'description': '你是否能做到每一个念头起落有觉，在关系中做到进退无碍、自洽舒服、容他欢喜#觉知#禅修#关系#詹唐宁#知识分享',
    //     'extras': '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',
    //     'feedType': 4,
    //     'nickname': '墨尔冥想',
    //     'thumbUrl': 'http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkia5a31eAjWic7aq6iaFanCDHdBwfA1lDss7vYlLeicoJRYmJWO8Q3jibGZasBvlj6WBlFkZxCHyp1fclLoC8XAXSvZicA&token=cztXnd9GyrEAKOSECretl8gga3LMEjDwVdv2iaKfx9NnUgFFNPc6f1NxUdgeSGq4uSicSp8ktic9QaECD7luyMhGmc4ricZ0jiaNsXia3ExrSePtsYpPJPl0ts0vIRWWZVgicQI&idx=1&bizid=1023&dotrans=0&hy=SZ&m=&scene=0&finder_expire_time=1728033170&finder_eid=export%2FUzFfAgtgekIEAQAAAAAAj4wSnYK1NQAAAAstQy6ubaLX4KHWvLEZgBPEzIFoFwIgANmFzNPgMIJBxowCIGVsfp8NDeTdPrxy',
    //     'url': 'https://channels.weixin.qq.com/web/pages/feed?eid=export%2FUzFfAgtgekIEAQAAAAAAj4wSnYK1NQAAAAstQy6ubaLX4KHWvLEZgBPEzIFoFwIgANmFzNPgMIJBxowCIGVsfp8NDeTdPrxy'
    //   }
    // })



  }, 30000)

  it('111', async () => {
    console.log(JSON.stringify(await JuziAPI.getCustomerInfo('1688857006556549', '7881300846030208'), null, 4))
  }, 60000)

  it('第 71 期 付款备注修改', async () => {
    const chats = await DataService.getChatsByCourseNo(71)

    for (const chat of chats) {
      if (chat.chat_state.state && (chat.chat_state.state as IChattingState).is_complete_payment) {
        await JuziAPI.updateUserAlias(chat.wx_id, chat.contact.wx_id, `F71${chat.contact.wx_name.slice(0, 10)}`)
      }
    }
  }, 1E8)

  it('修改备注', async () => {
    console.log(Config.setting?.wechatConfig?.id)
    console.log(Config.setting.wechatConfig?.id)

    await JuziAPI.updateUserAlias(Config.setting.wechatConfig?.id as string, '****************', '测试')

    console.log(JSON.stringify(await JuziAPI.getCustomerInfo('****************', '****************'), null, 4))
  }, 60000)

  it('获取客户信息', async () => {
    Config.setting.wechatConfig = await loadConfigByAccountName('moer11')
    console.log(JSON.stringify(await JuziAPI.getCustomerInfo('****************', '****************'), null, 4))
  }, 60000)

  it('获取群成员列表', async () => {
    console.log(JSON.stringify(await JuziAPI.getMembersList(1, 1000), null, 4))
  }, 30000)

  it('标签获取', async () => {
    console.log(JSON.stringify(await JuziAPI.getTags()))
    console.log(JSON.stringify(await JuziAPI.getGroups(), null, 4))
  }, 60000)

  it('获取客户详情', async () => {
    // console.log(JSON.stringify(await JuziAPI.getCustomerInfo('****************', '****************'), null, 4))

    Config.setting.wechatConfig = await loadConfigByAccountName('moer6')
    console.log(await JuziAPI.externalIdToWxId('wmXvL2CQAAkvYvLzlSfwuPTvahw29kug'))
  }, 60000)

  it('增加手机号', async () => {
    console.log(JSON.stringify(await JuziAPI.addPhoneNumber('****************', '****************', ['***********']), null, 4))
  }, 60000)

  it('测试 获取原图', async () => {
    console.log(JSON.stringify(await JuziAPI.getOriginalImage('66359424bd25acec2fff989f', '4af92e793f81bc699c64c94664301969'), null, 4))
  }, 60000)
})