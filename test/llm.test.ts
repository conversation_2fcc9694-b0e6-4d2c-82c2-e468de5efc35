import { LLM } from '../bot/lib/ai/llm/LLM'
import { DataService } from '../bot/service/moer/getter/getData'
import { isScheduleTimeAfter } from '../bot/service/moer/components/schedule/creat_schedule_task'

describe('Test', function () {
  beforeAll(() => {

  })

  it('date', async () => {
    const chat_id = '7881302221941051_1688856297674945'

    const currentTime = await DataService.getCurrentTime(chat_id)
    if (isScheduleTimeAfter(currentTime, { is_course_week: true, day: 1, time: '18:00:00' })) {
      console.log('here')
    }
  }, 60000)

  it('should pass', async () => {
    console.log(await LLM.predict(`根据以下指令，阅读客户的输入，然后将其分类到以下 4 个节点之一，并返回对应的节点编号：
1. 能量测评: 包括能量测试，客户提到能量评价或评分。
2. 财富果园解读: 关于财富果园的冥想和描述。
3. 销售场景: 包括购买课程、询价、或明确有购买意向。
4. 正常聊天: 不符合任何上述条件的对话。

如果客户的输入不符合以上任何节点的条件，返回 "null"。

客户输入：
<user_input>你妈死了</user_input>

请根据上述指令进行分类，先输出理由到 <reason></reason> 标签中，并将结果输出到 <answer></answer> 标签中 (仅包含节点编号或 "null")。

示例如下：
<reason>客户询问上课地点, 没有表示出明确出购买意向</reason>
<answer>4</answer>`, { model: 'gpt-5-mini' }))
  }, 60000)
})