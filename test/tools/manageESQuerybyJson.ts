import { FileHelper } from '../../bot/lib/file'
import { Client, ClientOptions } from '@elastic/elasticsearch'
import { Config } from '../../bot/config/config'
import * as path from 'path'
import { Document } from 'langchain/document'
import { ElasticClientArgs, ElasticVectorSearch } from '@langchain/community/vectorstores/elasticsearch'
import { AzureOpenAIEmbedding } from '../../bot/lib/ai/llm/openai_embedding'
import { ElasticSearchClient } from '../../bot/model/elastic_search/elastic_search'

interface QA {
    q: string
    a: string
    chunk: string
    doc: string
}
const clientConfig: ClientOptions = {
  node: Config.setting.elasticSearch.url,
  auth: {
    username: Config.setting.elasticSearch.username,
    password: Config.setting.elasticSearch.password,
  },
}

export async function manageESQuerybyJson(
  indexName: string,
  jsonFilePath: string,
  batchSize: number = 100
) {
  const data = JSON.parse(await FileHelper.readFile(jsonFilePath))
  const qas: QA[] = []
  data.forEach((item) => {
    const chunk = item.chunk
    const doc = item.doc
    qas.push({
      q: item.q,
      a: item.a,
      chunk: item.chunk,
      doc: item.doc
    })

  })

  // Embedding 插入
  // 构建 embedding, 插入到 es 中
  const clientArgs: ElasticClientArgs = {
    client: new Client(clientConfig),
    indexName: indexName,
  }

  // Index documents
  const docs = qas.map((item) => {
    return new Document({
      metadata: item,
      pageContent: item.q,
    })
  })

  console.log(docs.length)

  const batches = Math.ceil(docs.length / batchSize)
  for (let i = 0; i < batches; i++) {
    const batch = docs.slice(i * batchSize, (i + 1) * batchSize)
    const embeddings = AzureOpenAIEmbedding.getInstance()
    const vectorStore = new ElasticVectorSearch(embeddings, clientArgs)
    await vectorStore.addDocuments (batch)
    console.log(`Indexed ${100 * (i + 1)} documents`)
  }
}

export async function deleteQueryFromESbyJson(
  indexName: string,
  jsonFilePath: string,
) {
  const client = new Client(clientConfig)

  try {
    // 读取 deleteQuery.json 文件
    const deleteQueries = JSON.parse(await FileHelper.readFile(jsonFilePath))

    for (const item of deleteQueries) {
      const query = {
        term: {
          'metadata.q': item.q
        }
      }

      // 查询匹配的文档数量
      const searchResponse = await client.search({
        index: indexName,
        body: {
          query: query,
          size: 0
        }
      })

      if (!searchResponse.hits || searchResponse.hits.total === undefined) {
        throw new Error('Unexpected search response structure')
      }

      const matchCount = typeof searchResponse.hits.total === 'number'
        ? searchResponse.hits.total
        : searchResponse.hits.total.value

      console.log(`删除: "${item.q}"`)

      if (matchCount === 1) {
        // 如果只有一个匹配项，执行删除操作
        const deleteResponse = await client.deleteByQuery({
          index: indexName,
          body: {
            query: query
          }
        })

        console.log(`成功删除 ${deleteResponse.deleted} 个文档`)
      } else if (matchCount === 0) {
        console.log('没有找到匹配的文档')
      } else {
        console.log(`找到 ${matchCount} 个匹配的文档，超过一个，不执行删除操作`)

        // 打印出查询到的文档内容
        const detailedSearchResponse = await client.search({
          index: indexName,
          body: {
            query: query,
            size: matchCount
          }
        })

        console.log('匹配的文档：', JSON.stringify(detailedSearchResponse.hits.hits, null, 2))
      }

      console.log('---') // 分隔不同查询的输出
    }
  } catch (error) {
    console.error('操作过程中出错:', error)
  }
}

export async function downloadDataFromES(indexName: string, jsonFilePath: string) {
  const client = ElasticSearchClient.getInstance()
  const scrollTimeout = '10m'
  try {
    let searchResponse = await client.search({
      index: indexName,
      scroll: scrollTimeout,
      body: {
        query: {
          match_all: {}
        },
        size: 10000
      }
    })

    const hits:any[] = []
    while (searchResponse.hits.hits.length) {
      hits.push(...searchResponse.hits.hits)
      searchResponse = await client.scroll({
        scroll_id: searchResponse._scroll_id,
        scroll: scrollTimeout
      })
    }

    const data = hits.reduce((acc, hit:any) => {
      const doc = hit._source.metadata.doc
      if (!acc[doc]) {
        acc[doc] = []
      }
      acc[doc].push(hit._source.metadata)
      return acc
    }, {})

    for (const [doc, docData] of Object.entries(data)) {
      const docFilePath = path.join(jsonFilePath, renameDoc(doc))
      await FileHelper.writeFile(docFilePath, JSON.stringify(docData, null, 2))
      console.log(`Data successfully downloaded to ${docFilePath}`)
    }
  } catch (error) {
    console.error('Error downloading data:', error)
  }
}

function renameDoc(docName: string) {
  const ext = path.extname(docName)
  if (ext !== '.json') {
    return `${path.basename(docName, ext)}.json`
  }
  return docName
}
