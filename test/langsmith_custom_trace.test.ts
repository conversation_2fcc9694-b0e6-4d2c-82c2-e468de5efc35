import { Config } from '../bot/config/config'
import { traceable } from 'langsmith/traceable'
import { PromptTemplate } from '@langchain/core/prompts'
import { UUID } from '../bot/lib/uuid/uuid'
import { LLM } from '../bot/lib/ai/llm/LLM'
import { Client, RunTree, RunTreeConfig } from 'langsmith'
import { CheapOpenAIClient } from '../bot/lib/ai/llm/client'
import { StringOutputParser } from '@langchain/core/output_parsers'
import { faker } from '@faker-js/faker'

export class Test1Prompt {
  public static async format(param: { name: string; info: string; logInfo?: {round_id: string} }) {
    return PromptTemplate.fromTemplate('Hi, I am {name}{info}').format(param)
  }
}

export class Test2Prompt {
  public static async format(param: { name: string; info: string; logInfo?: {round_id: string} }) {
    return PromptTemplate.fromTemplate('Hello, I am {name}{info}').format(param)
  }
}

describe('Test', function () {
  beforeAll(() => {
    process.env.LANGCHAIN_API_KEY = Config.setting.langsmith.apiKey
    process.env.LANGCHAIN_TRACING_V2 = 'true'
    process.env.LANGCHAIN_PROJECT = 'prompt-canvas'
  })

  it('should pass', async () => {
    const rag = traceable(async function rag(input: { question: string; answer: string }) {
      return 'fku'
    })

    await rag({ question: 'fk', answer: 'u' })
  }, 60000)


  it('test', async () => {
    const round_id = UUID.v4()
    console.log(round_id)

    async function PWrapper(c: any, fc: (...arg: any[]) => Promise<string>, param: any, options: any): Promise<string> {
      const wrapper = traceable(fc.bind(c), options)

      return wrapper(param)
    }

    // const prompt2 =  await PWrapper(Test2Prompt, Test2Prompt.format, { name: `Jack_${round_id}2`, info: 'u' }, {
    //   parent_run_id: round_id,
    // })

    await LLM.predict('hi', { meta: { round_id } })

    const prompt1 =  await PWrapper(Test1Prompt, Test1Prompt.format, { name: `Jack_${round_id}1`, info: 'u' }, {
      parent_run_id: round_id,
      id: UUID.v4(),
      start_time: Date.now(),
      trace_id: round_id
    })

    await LLM.predict(prompt1, { meta: { round_id } })

    // await LLM.predict(prompt2, { meta: { round_id } })
  }, 60000)


  it('custom', async () => {
    const round_id = UUID.v4()

    await LLM.predict('hi', { meta: { round_id } })

    const parentRunConfig: RunTreeConfig = {
      name: 'My Chat Bot',
      run_type: 'chain',
      inputs: {
        text: 'Summarize this morning\'s meetings.',
      },
      // id: round_id,
      parent_run_id: '841b44a5-9982-4df5-8e95-8504c4afec98',
      // trace_id: '841b44a5-9982-4df5-8e95-8504c4afec98',
      // serialized: {},
    }

    const parentRun = new RunTree(parentRunConfig)

    await parentRun.postRun()

    await parentRun.end({
      outputs: {
        output: ['The meeting notes are as follows:...'],
      },
    })

    // False directs to not exclude child runs
    await parentRun.patchRun()
  }, 60000)

  it('test1', async () => {
    const client =  CheapOpenAIClient.getClient('gpt-4.1')
    const prompt = PromptTemplate.fromTemplate('hi {name}, how {are}')

    const parser = new StringOutputParser()
    await prompt.pipe(client).pipe(parser).invoke({ name: 'jack', are: 'you' })
  }, 60000)


  it('query langSmith', async () => {
    const client = new Client()
    const key = 'promptName'
    const value = 'MergeSlotArray'

    const runs = client.listRuns({
      projectName: 'moer',
      filter: `and(eq(metadata_key, '${key}'), eq(metadata_value, '${value}'))`,
      error: false
    })

    for await (const run of runs) {
      if (run.name === 'StrOutputParser') continue

      if (run.name === 'AzureChatOpenAI') {
        const input = run.inputs.messages[0][0].kwargs.content
        const output = run.outputs?.generations[0][0].text
        console.log(input, output)
      }
    }

  }, 60000)

  it('mock 一个 promptName, 从推送 prompt，到从 trace 构建数据集', async () => {
    const prompt = PromptTemplate.fromTemplate('hi {name}, good {event}')

    for (let i = 0; i < 5; i++) {
      await LLM.predict(prompt, { meta: { promptName: 'TestPrompt' }, projectName: 'prompt-canvas' }, { name: faker.person.fullName(), event: faker.word.noun() })
    }

    // const client = new Client()
    // await client.pushPrompt('test_prompt_v1', {
    //   object: prompt
    // })
  }, 60000)

  it('拉取数据，并新建 DataSet', async () => {
    const client = new Client()
    const key = 'promptName'
    const value = 'TestPrompt'

    // Create a dataset to store the examples
    const datasetName = `TestPrompt Dataset_${new Date().toLocaleString()}`
    const dataset = await client.createDataset(datasetName, {
      description: 'Examples collected from TestPrompt runs'
    })

    // Object to group runs by trace_id
    const runsByTraceId: Record<string, {
      input?: any
      output?: any
      metadata?: any
    }> = {}

    // Query runs matching your filter criteria
    const runs = client.listRuns({
      projectName: 'moer',
      filter: `and(eq(metadata_key, '${key}'), eq(metadata_value, '${value}'))`,
      error: false
    })

    // First pass: group runs by trace_id
    for await (const run of runs) {
      const traceId = run.trace_id

      if (!traceId) continue

      if (!runsByTraceId[traceId]) {
        runsByTraceId[traceId] = {
          metadata: {
            run_id: run.id,
            trace_id: traceId,
            timestamp: run.start_time,
          }
        }
      }

      // Extract input from PromptTemplate runs
      if (run.name === 'PromptTemplate') {
        runsByTraceId[traceId].input = run.inputs
      }

      // Extract output from StrOutputParser runs
      if (run.name === 'StrOutputParser' && run.outputs?.output) {
        runsByTraceId[traceId].output = { output: run.outputs.output }
      }
    }

    // Arrays to collect inputs, outputs, and metadata
    const inputs: any[] = []
    const outputs: any[] = []
    const metadata: any[] = []

    // Second pass: collect complete examples
    for (const traceId in runsByTraceId) {
      const runData = runsByTraceId[traceId]

      if (runData.input && runData.output) {
        inputs.push(runData.input)
        outputs.push(runData.output)
        metadata.push(runData.metadata)
      }
    }

    // Create examples in bulk if we have collected any
    if (inputs.length > 0) {
      await client.createExamples({
        inputs,
        outputs,
        metadata,
        datasetId: dataset.id,
      })

      console.log(`Created dataset "${datasetName}" with ${inputs.length} examples`)
    } else {
      console.log('No matching runs found to create examples')
    }
  }, 60000)
})