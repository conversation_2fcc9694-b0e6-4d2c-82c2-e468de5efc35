import { PrismaMongoClient } from '../bot/model/mongodb/prisma'

interface IMoerEnterpriseConfig {
  notifyGroupId: string
  classGroupId: string
  classGroupIds?: string[]
  isGroupOwner?: boolean
  proxyGroupNotify?: boolean
  oldClassGroupId?: string // 新增字段，用于保存原来的群ID
}

// 需要更新的账号列表
const ACCOUNTS_TO_UPDATE = [
  '****************',
  '****************', 
  '****************',
  '****************',
  '****************',
  '****************',
  '****************'
]

// 新的群ID
const NEW_CLASS_GROUP_ID = 'R:*****************'

async function updateGroupConfig() {
  console.log('开始更新群配置...')
  
  try {
    for (const wechatId of ACCOUNTS_TO_UPDATE) {
      console.log(`正在更新账号: ${wechatId}`)
      
      // 查找账号配置
      const config = await PrismaMongoClient.getConfigInstance().config.findFirst({
        where: {
          wechatId: wechatId,
          enterpriseName: 'moer'
        }
      })
      
      if (!config) {
        console.log(`未找到账号 ${wechatId} 的配置`)
        continue
      }
      
      // 获取当前的 enterpriseConfig
      const currentConfig = config.enterpriseConfig as unknown as IMoerEnterpriseConfig
      
      if (!currentConfig) {
        console.log(`账号 ${wechatId} 没有 enterpriseConfig`)
        continue
      }
      
      // 保存原来的 classGroupId 到 oldClassGroupId
      const updatedConfig: IMoerEnterpriseConfig = {
        ...currentConfig,
        oldClassGroupId: currentConfig.classGroupId, // 保存原来的群ID
        classGroupId: NEW_CLASS_GROUP_ID // 设置新的群ID
      }
      
      // 更新配置
      await PrismaMongoClient.getConfigInstance().config.update({
        where: {
          id: config.id
        },
        data: {
          enterpriseConfig: updatedConfig
        }
      })
      
      console.log(`账号 ${wechatId} 配置更新成功:`)
      console.log(`  原群ID: ${currentConfig.classGroupId} -> oldClassGroupId`)
      console.log(`  新群ID: ${NEW_CLASS_GROUP_ID} -> classGroupId`)
    }
    
    console.log('所有账号配置更新完成!')
    
  } catch (error) {
    console.error('更新配置时发生错误:', error)
  } finally {
    await PrismaMongoClient.getConfigInstance().$disconnect()
  }
}

// 回滚函数，用于恢复原来的配置
async function rollbackGroupConfig() {
  console.log('开始回滚群配置...')
  
  try {
    for (const wechatId of ACCOUNTS_TO_UPDATE) {
      console.log(`正在回滚账号: ${wechatId}`)
      
      // 查找账号配置
      const config = await PrismaMongoClient.getConfigInstance().config.findFirst({
        where: {
          wechatId: wechatId,
          enterpriseName: 'moer'
        }
      })
      
      if (!config) {
        console.log(`未找到账号 ${wechatId} 的配置`)
        continue
      }
      
      // 获取当前的 enterpriseConfig
      const currentConfig = config.enterpriseConfig as unknown as IMoerEnterpriseConfig
      
      if (!currentConfig || !currentConfig.oldClassGroupId) {
        console.log(`账号 ${wechatId} 没有 oldClassGroupId，无法回滚`)
        continue
      }
      
      // 恢复原来的配置
      const restoredConfig: IMoerEnterpriseConfig = {
        ...currentConfig,
        classGroupId: currentConfig.oldClassGroupId, // 恢复原来的群ID
        oldClassGroupId: undefined // 清除 oldClassGroupId
      }
      
      // 删除 oldClassGroupId 字段
      delete restoredConfig.oldClassGroupId
      
      // 更新配置
      await PrismaMongoClient.getConfigInstance().config.update({
        where: {
          id: config.id
        },
        data: {
          enterpriseConfig: restoredConfig
        }
      })
      
      console.log(`账号 ${wechatId} 配置回滚成功:`)
      console.log(`  恢复群ID: ${currentConfig.oldClassGroupId} -> classGroupId`)
    }
    
    console.log('所有账号配置回滚完成!')
    
  } catch (error) {
    console.error('回滚配置时发生错误:', error)
  } finally {
    await PrismaMongoClient.getConfigInstance().$disconnect()
  }
}

// 查看当前配置
async function viewCurrentConfig() {
  console.log('查看当前配置...')
  
  try {
    for (const wechatId of ACCOUNTS_TO_UPDATE) {
      const config = await PrismaMongoClient.getConfigInstance().config.findFirst({
        where: {
          wechatId: wechatId,
          enterpriseName: 'moer'
        }
      })
      
      if (config) {
        const enterpriseConfig = config.enterpriseConfig as unknown as IMoerEnterpriseConfig
        console.log(`账号 ${wechatId} (${config.accountName}):`)
        console.log(`  当前群ID: ${enterpriseConfig.classGroupId}`)
        console.log(`  原群ID: ${enterpriseConfig.oldClassGroupId || '无'}`)
        console.log('---')
      }
    }
  } catch (error) {
    console.error('查看配置时发生错误:', error)
  } finally {
    await PrismaMongoClient.getConfigInstance().$disconnect()
  }
}

// 根据命令行参数执行不同操作
const action = process.argv[2]

switch (action) {
  case 'update':
    updateGroupConfig()
    break
  case 'rollback':
    rollbackGroupConfig()
    break
  case 'view':
    viewCurrentConfig()
    break
  default:
    console.log('使用方法:')
    console.log('  npm run ts-node scripts/update_group_config.ts update   # 更新配置')
    console.log('  npm run ts-node scripts/update_group_config.ts rollback # 回滚配置')
    console.log('  npm run ts-node scripts/update_group_config.ts view     # 查看配置')
}
