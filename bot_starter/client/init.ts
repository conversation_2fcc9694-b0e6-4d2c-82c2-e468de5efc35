import chalk from 'chalk'
import { Config, MoerAccountType } from '../../bot/config/config'
import { Account, ClientAccountConfig } from '../config/account'
import { WechatAccountDB } from '../../bot/service/moer/database/user'
import logger from '../../bot/model/logger/logger'
import { startWorkersForExistingChats } from '../../bot/service/moer/components/flow/schedule/task_starter'
import { DataService } from '../../bot/service/moer/getter/getData'
import { ClassGroupTaskManager } from './class_group'
import { fixDate } from '../../bot/lib/date/mock'
import { DateHelper } from '../../bot/lib/date/date'
import { OriginalDate } from '../../bot/lib/date/jump'
import { Queue, Worker } from 'bullmq'
import { RedisDB } from '../../bot/model/redis/redis'
import { MoerAP<PERSON> } from '../../bot/model/moer_api/moer'
import { GlobalMessageHandlerService } from '../../bot/service/message/message_merge'
import { ObjectUtil } from '../../bot/lib/object'
import {
  MoerVisualizedSopProcessor
} from '../../bot/service/moer/components/visualized_sop/moer_visualized_sop_processor'
import { AccountTask } from '../../bot/service/moer/components/flow/schedule/task/accountTask'


async function InsertAccountInfo(account: Account) {
  if (!await WechatAccountDB.getById(account.wechatId)) {
    await WechatAccountDB.create({
      id: account.wechatId,
      name: account.nickname,
      avatar: null
    })

    logger.trace('新建账号：', account.nickname)
  }
}

async function addDateByOneDay() {
  // 设定一个 0点更新日期的任务
  // 清除已有的重复任务
  // 创建队列
  const queue = new Queue(`dateAdjustment_${Config.setting.wechatConfig?.id}`, {
    connection: RedisDB.getInstance()
  })

  await queue.obliterate({ force: true })

  await queue.add(
    'daily-date-adjustment',
    {},
    {
      repeat: {
        pattern: '0 0 * * *', // 每天零点执行
        tz: 'Asia/Shanghai', // 设置时区
      },
    }
  )

  // 处理日期调整的工作进程
  new Worker(`dateAdjustment_${Config.setting.wechatConfig?.id}`, async (job) => {
    try {
      fixDate(DateHelper.add(new OriginalDate(), 1, 'day').toLocaleString())

      logger.trace(`Date adjusted to: ${new Date().toLocaleString()}`)
    } catch (error) {
      logger.error('Error adjusting date:', error)
    }
  }, {
    connection: RedisDB.getInstance()
  }).on('error', (err) => {
    logger.error('dateAdjustment Worker 发生未捕获错误', err)
  })

  // 初始化当前时间
  fixDate(DateHelper.add(new OriginalDate(), 1, 'day').toLocaleString())
  logger.trace(`当前日期: ${new Date().toLocaleString()}`)
}

export async function initConfig() {
  // 环境配置
  const env = process.env.NODE_ENV
  if (!env) {
    console.error('请设置环境变量 NODE_ENV')
    process.exit(1)
  }

  Config.setting.startTime = Date.now()
  Config.setting.AGENT_NAME = '麦子老师'

  // 注入配置
  // 读取注入的 姓名
  const name = process.env.WECHAT_NAME
  if (!name) {
    console.error('请设置环境变量 WECHAT_NAME')
    process.exit(1)
  }

  const account = await ClientAccountConfig.getAccountByName(name)
  if (!account) {
    console.error(`找不到${name}对应的账号`)
    process.exit(1)
  }

  const currentCourseNo = DataService.getCurrentWeekCourseNo()

  Config.setting.wechatConfig = {
    orgToken: account.orgToken,
    id: account.wechatId,
    name: account.nickname,
    botUserId: account.botUserId,
    notifyGroupId: account.notifyGroupId,
    classGroupId: account.classGroupId,
    classGroupIds: account.classGroupIds,
    courseNo: currentCourseNo,
    isGroupOwner: account.isGroupOwner,
    proxyGroupNotify: account.proxyGroupNotify
  }

  console.log(chalk.green(`当前账号：${account.nickname}(${account.wechatId}) ${ObjectUtil.enumValueToKey(MoerAccountType, Config.getAccountType())}`))

  if (Config.setting.onlyReceiveMessage) { // mode
    console.log(chalk.redBright('开启仅接收消息模式，请注意此模式下只接受消息，不会进行AI回复'))
  }

  if (Config.isOnlineTestAccount()) { // 线上测试账号，将日期提前一天，并建立一个定时任务，每天 0 点更新时间。
    await addDateByOneDay()
  }

  // 启动消息处理
  GlobalMessageHandlerService.startWorker()

  // 加载课程信息
  await initCourseInfo(currentCourseNo)

  // 数据库插入账号信息
  await InsertAccountInfo(account)

  // 监听账号消息
  await AccountTask.startWorkerForAccount()

  // 监听课程群任务
  ClassGroupTaskManager.startWorker()

  // 启动任务监听
  await startWorkersForExistingChats()


  //监听可视化sop
  new MoerVisualizedSopProcessor().start()
}


async function initCourseInfo(courseNo: number) {
  const courseInfo = await MoerAPI.getCurrentCourseInfo(courseNo)

  if (courseInfo.code !== 0) {
    throw new Error(`获取课程信息失败: ${courseInfo.msg}`)
  }
  const course = courseInfo.data

  console.log(chalk.redBright(`第 ${courseNo} 期 ${course.name}， 开课时间 ${course.startTime}`))
}