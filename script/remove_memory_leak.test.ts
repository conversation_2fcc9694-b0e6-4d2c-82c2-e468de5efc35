import { Queue } from 'bullmq'
import { PrismaMongoClient } from '../bot/model/mongodb/prisma'
import { RedisDB } from '../bot/model/redis/redis'
import { getVisualizedSopQueueName } from '../bot/service/moer/components/visualized_sop/visualized_sop_task_starter'
describe('remove', () => {
  jest.setTimeout(100000)
  test('remove', async() => {
    const mongoClient = PrismaMongoClient.getConfigInstance()
    const accounts = await mongoClient.config.findMany({ where:{ enterpriseName:'moer' }, select:{ wechatId:true, accountName:true } })
    for (const account of accounts) {
      // console.log(account.accountName, account.wechatId)
      // const generalKey = getGeneralSopKey(account.wechatId)
      const sopKey = getVisualizedSopQueueName(account.wechatId)
      const queue = new Queue(sopKey, {
        connection: RedisDB.getInstance()
      })
      const tasks = await queue.getCompletedCount()
      console.log(tasks)
      // await queue.clean(60000, 200000, 'completed')
      // return
    }
  })
})