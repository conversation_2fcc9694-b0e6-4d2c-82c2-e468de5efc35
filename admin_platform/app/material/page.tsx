'use client'

import { useState } from 'react'
import MaterialShow from '../component/material/materialShow'
import { Material, queryMaterials, getMaterialTypes, updateMaterial, getMainCategories, getSubCategories, deleteMaterial, toggleMaterialEnable, getDocOptions } from '@/app/api/material'

const MATERIAL_TYPES = {
  101: '文本',
  102: '图片',
  103: '视频',
  104: '文件',
  105: '音频',
  106: '链接小卡片',
  107: '视频号',
}

const DOC_OPTIONS = ['全局', '售前', '售中', '售后']

export default function MaterialPage() {
  const handlePreview = (_material: Material) => {
    // This function will be handled by the MaterialShow component
  }

  const getPreviewContent = (material: Material) => {
    const contentData = material.data as any

    switch (material.type) {
      case 101: // 文本类型
        return (
          <div className="w-16 h-16 bg-blue-100 rounded flex items-center justify-center cursor-pointer"
            onClick={() => handlePreview(material)}>
            <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
        )
      case 102: // 图片类型
        if (contentData?.url) {
          return (
            <div className="w-16 h-16 overflow-hidden rounded cursor-pointer"
              onClick={() => handlePreview(material)}>
              <img
                src={contentData.url}
                alt={material.title}
                className="w-full h-full object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMS4zMzMzIDQyLjY2NjdMMzIgMzJMMzcuMzMzMyAzNy4zMzMzTDQyLjY2NjcgMzJMMzggMjcuMzMzM0gyNlYzOS4zMzMzTDIxLjMzMzMgNDIuNjY2N1oiIGZpbGw9IiM5Q0E0QUYiLz4KPC9zdmc+'
                }}
              />
            </div>
          )
        }
        break

      case 103: // 视频类型
        return (
          <div className="w-16 h-16 bg-red-100 rounded flex items-center justify-center cursor-pointer"
            onClick={() => handlePreview(material)}>
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293L12 11l.707-.707A1 1 0 0113.414 10H15M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
          </div>
        )
      case 104: // 文件类型
        return (
          <div className="w-16 h-16 bg-green-100 rounded flex items-center justify-center cursor-pointer"
            onClick={() => handlePreview(material)}>
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <div className="absolute bottom-0 right-0 bg-white text-xs px-1 rounded-tl text-green-600 font-semibold">
              {contentData?.fileName ? contentData.fileName.split('.').pop()?.toUpperCase() : 'FILE'}
            </div>
          </div>
        )
      case 106: // 链接小卡片类型
        if (contentData?.thumbnailUrl) {
          return (
            <div className="w-16 h-16 overflow-hidden rounded cursor-pointer"
              onClick={() => handlePreview(material)}>
              <img
                src={contentData.thumbnailUrl}
                alt={material.title}
                className="w-full h-full object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMS4zMzMzIDQyLjY2NjdMMzIgMzJMMzcuMzMzMyAzNy4zMzMzTDQyLjY2NjcgMzJMMzggMjcuMzMzM0gyNlYzOS4zMzMzTDIxLjMzMzMgNDIuNjY2N1oiIGZpbGw9IiM5Q0E0QUYiLz4KPC9zdmc+'
                }}
              />
            </div>
          )
        }
        break

      case 107: // 视频号类型
        if (contentData?.coverUrl) {
          return (
            <div className="w-16 h-16 overflow-hidden rounded cursor-pointer"
              onClick={() => handlePreview(material)}>
              <img
                src={contentData.coverUrl}
                alt={material.title}
                className="w-full h-full object-cover"
                onError={(e) => {
                  (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMS4zMzMzIDQyLjY2NjdMMzIgMzJMMzcuMzMzMyAzNy4zMzMzTDQyLjY2NjcgMzJMMzggMjcuMzMzM0gyNlYzOS4zMzMzTDIxLjMzMzMgNDIuNjY2N1oiIGZpbGw9IiM5Q0E0QUYiLz4KPC9zdmc+'
                }}
              />
            </div>
          )
        }
        break

      default:
        return null
    }
  }

  const renderPreviewContent = (material: Material) => {
    const contentData = material.data as any

    switch (material.type) {
      case 101: // 文本类型
        const textContent = contentData?.content || material.title
        return (
          <div className="bg-base-100 p-6 rounded-lg">
            <h3 className="text-lg font-bold mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              文本内容
            </h3>
            <div className="bg-white p-4 rounded border max-h-96 overflow-auto">
              <pre className="whitespace-pre-wrap text-sm leading-relaxed">{textContent}</pre>
            </div>
          </div>
        )

      case 102: // 图片类型
        if (contentData?.url) {
          return (
            <div className="bg-base-100 p-6 rounded-lg">
              <h3 className="text-lg font-bold mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                图片预览
              </h3>
              <div className="bg-white p-4 rounded border">
                <img
                  src={contentData.url}
                  alt={material.title}
                  className="max-w-full max-h-96 mx-auto rounded shadow-sm"
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = 'none'
                    const errorDiv = document.createElement('div')
                    errorDiv.className = 'text-center text-gray-500 p-8'
                    errorDiv.innerHTML = '图片加载失败';
                    (e.target as HTMLImageElement).parentNode?.appendChild(errorDiv)
                  }}
                />
                <div className="mt-4 text-sm text-gray-600">
                  <p><span className="font-semibold">文件名：</span>{contentData.fileName || '未知'}</p>
                  <p><span className="font-semibold">文件大小：</span>{contentData.fileSize ? `${(contentData.fileSize / 1024).toFixed(2)} KB` : '未知'}</p>
                  <p><span className="font-semibold">文件类型：</span>{contentData.mimeType || '未知'}</p>
                </div>
              </div>
            </div>
          )
        }
        break

      case 103: // 视频类型
        if (contentData?.url) {
          return (
            <div className="bg-base-100 p-6 rounded-lg">
              <h3 className="text-lg font-bold mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                视频预览
              </h3>
              <div className="bg-white p-4 rounded border">
                <video
                  src={contentData.url}
                  controls
                  className="max-w-full max-h-96 mx-auto rounded shadow-sm"
                  onError={(e) => {
                    (e.target as HTMLVideoElement).style.display = 'none'
                    const errorDiv = document.createElement('div')
                    errorDiv.className = 'text-center text-gray-500 p-8'
                    errorDiv.innerHTML = '视频加载失败';
                    (e.target as HTMLVideoElement).parentNode?.appendChild(errorDiv)
                  }}
                >
                  您的浏览器不支持视频播放
                </video>
                <div className="mt-4 text-sm text-gray-600">
                  <p><span className="font-semibold">文件名：</span>{contentData.fileName || '未知'}</p>
                  <p><span className="font-semibold">文件大小：</span>{contentData.fileSize ? `${(contentData.fileSize / 1024 / 1024).toFixed(2)} MB` : '未知'}</p>
                  <p><span className="font-semibold">文件类型：</span>{contentData.mimeType || '未知'}</p>
                </div>
              </div>
            </div>
          )
        }
        break

      case 104: // 文件类型
        if (contentData?.url) {
          return (
            <div className="bg-base-100 p-6 rounded-lg">
              <h3 className="text-lg font-bold mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                文件信息
              </h3>
              <div className="bg-white p-4 rounded border">
                <div className="flex items-center justify-center p-8 bg-gray-50 rounded mb-4">
                  <div className="text-center">
                    <svg className="w-16 h-16 text-green-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <p className="text-sm text-gray-600">{contentData.fileName || '文件'}</p>
                  </div>
                </div>
                <div className="space-y-2 text-sm">
                  <p><span className="font-semibold">文件名：</span>{contentData.fileName || '未知'}</p>
                  <p><span className="font-semibold">文件大小：</span>{contentData.fileSize ? `${(contentData.fileSize / 1024).toFixed(2)} KB` : '未知'}</p>
                  <p><span className="font-semibold">文件类型：</span>{contentData.mimeType || '未知'}</p>
                </div>
                <div className="mt-4">
                  <a
                    href={contentData.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    下载文件
                  </a>
                </div>
              </div>
            </div>
          )
        }
        break

      case 106: // 链接小卡片类型
        return (
          <div className="bg-base-100 p-6 rounded-lg">
            <h3 className="text-lg font-bold mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
              链接小卡片
            </h3>
            <div className="bg-white p-4 rounded border">
              {contentData?.thumbnailUrl && (
                <img
                  src={contentData.thumbnailUrl}
                  alt={material.title}
                  className="max-w-full max-h-96 mx-auto rounded shadow-sm mb-4"
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = 'none'
                    const errorDiv = document.createElement('div')
                    errorDiv.className = 'text-center text-gray-500 p-8'
                    errorDiv.innerHTML = '缩略图加载失败';
                    (e.target as HTMLImageElement).parentNode?.appendChild(errorDiv)
                  }}
                />
              )}
              <div className="space-y-2 text-sm">
                <p><span className="font-semibold">标题：</span>{material.title}</p>
                {contentData?.url && <p><span className="font-semibold">链接：</span><a href={contentData.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">{contentData.url}</a></p>}
                {contentData?.description && <p><span className="font-semibold">描述：</span>{contentData.description}</p>}
              </div>
            </div>
          </div>
        )

      case 107: // 视频号类型
        return (
          <div className="bg-base-100 p-6 rounded-lg">
            <h3 className="text-lg font-bold mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              视频号
            </h3>
            <div className="bg-white p-4 rounded border">
              {contentData?.coverUrl && (
                <img
                  src={contentData.coverUrl}
                  alt={material.title}
                  className="max-w-full max-h-96 mx-auto rounded shadow-sm mb-4"
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = 'none'
                    const errorDiv = document.createElement('div')
                    errorDiv.className = 'text-center text-gray-500 p-8'
                    errorDiv.innerHTML = '封面加载失败';
                    (e.target as HTMLImageElement).parentNode?.appendChild(errorDiv)
                  }}
                />
              )}
              <div className="space-y-2 text-sm">
                <p><span className="font-semibold">标题：</span>{material.title}</p>
                {contentData?.url && <p><span className="font-semibold">视频链接：</span><a href={contentData.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">{contentData.url}</a></p>}
                {contentData?.description && <p><span className="font-semibold">描述：</span>{contentData.description}</p>}
              </div>
            </div>
          </div>
        )

      default:
        return (
          <div className="bg-base-100 p-4 rounded-lg">
            <h3 className="font-bold mb-2">原始数据：</h3>
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-96">
              {JSON.stringify(contentData, null, 2)}
            </pre>
          </div>
        )
    }
  }

  return (
    <div>
      <MaterialShow
        queryMaterials={queryMaterials}
        getMaterialTypes={getMaterialTypes}
        updateMaterial={updateMaterial}
        getMainCategories={getMainCategories}
        getSubCategories={getSubCategories}
        deleteMaterial={deleteMaterial}
        toggleMaterialEnable={toggleMaterialEnable}
        getDocOptions={getDocOptions}
        getPreviewContent={getPreviewContent}
        renderPreviewContent={renderPreviewContent}
        MATERIAL_TYPES={MATERIAL_TYPES}
        DOC_OPTIONS={DOC_OPTIONS}
      />
    </div>
  )
}