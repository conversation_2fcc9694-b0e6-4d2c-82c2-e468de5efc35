'use client'

import { useState } from 'react'
import { toast } from 'react-toastify'
import { putObjectStream } from '@/app/rag/aliyunoss'
import { createMaterial } from '@/app/api/material'

const MATERIAL_TYPES = {
  101: '文本',
  102: '图片',
  103: '视频',
  104: '文件',
}

const DOC_OPTIONS = ['全局', '售前', '售中', '售后']

interface MaterialFormData {
  type: number
  title: string
  description: string
  main_category: string
  sub_category: string
  doc: string
  content: string
  file?: File
}

export default function MaterialUploadPage() {
  const [formData, setFormData] = useState<MaterialFormData>({
    type: 101,
    title: '',
    description: '',
    main_category: '',
    sub_category: '',
    doc: '',
    content: ''
  })
  const [uploading, setUploading] = useState(false)
  const [filePreview, setFilePreview] = useState<string | null>(null)

  const handleTypeChange = (type: number) => {
    setFormData((prev) => ({ ...prev, type }))
    setFilePreview(null)
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setFormData((prev) => ({ ...prev, file }))

      // 为图片和视频创建预览
      if (file.type.startsWith('image/')) {
        const reader = new FileReader()
        reader.onload = (event) => setFilePreview(event.target?.result as string)
        reader.readAsDataURL(file)
      } else {
        setFilePreview(null)
      }
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.title.trim()) {
      toast.error('请输入标题')
      return
    }

    if (!formData.main_category.trim()) {
      toast.error('请输入主分类')
      return
    }

    if (!formData.sub_category.trim()) {
      toast.error('请输入子分类')
      return
    }

    if (!formData.description.trim()) {
      toast.error('请输入描述')
      return
    }

    if (!formData.doc) {
      toast.error('请选择Doc选项')
      return
    }

    if (formData.type === 101 && !formData.content.trim()) {
      toast.error('请输入文本内容')
      return
    }

    if ([102, 103, 104].includes(formData.type) && !formData.file) {
      toast.error('请选择文件')
      return
    }

    setUploading(true)

    try {
      const materialData: any = {
        type: formData.type,
        title: formData.title,
        description: formData.description,
        main_category: formData.main_category,
        sub_category: formData.sub_category,
        doc: formData.doc,
      }

      // 处理文本素材
      if (formData.type === 101) {
        materialData.data = { content: formData.content }
      }
      // 处理文件素材（图片、视频、文件）
      else if (formData.file) {
        const fileName = `${Date.now()}_${formData.file.name}`
        const fileUrl = await putObjectStream(fileName, formData.file, 'moer/material')

        materialData.data = {
          url: fileUrl,
          fileName: formData.file.name,
          fileSize: formData.file.size,
          mimeType: formData.file.type
        }
      }

      await createMaterial(materialData)

      toast.success('素材上传成功！')

      // 重置表单
      setFormData({
        type: 101,
        title: '',
        description: '',
        main_category: '',
        sub_category: '',
        doc: '',
        content: ''
      })
      setFilePreview(null)

      // 清除文件输入
      const fileInput = document.getElementById('fileInput') as HTMLInputElement
      if (fileInput) fileInput.value = ''

    } catch (error) {
      console.error('上传失败:', error)
      toast.error(`上传失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setUploading(false)
    }
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">上传素材</h1>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* 素材类型选择 */}
        <div>
          <label className="label">
            <span className="label-text font-medium">素材类型</span>
          </label>
          <div className="flex flex-wrap gap-2">
            {Object.entries(MATERIAL_TYPES).map(([typeId, typeName]) => (
              <button
                key={typeId}
                type="button"
                className={`btn btn-sm ${formData.type === parseInt(typeId, 10) ? 'btn-primary' : 'btn-outline'}`}
                onClick={() => handleTypeChange(parseInt(typeId, 10))}
              >
                {typeName}
              </button>
            ))}
          </div>
        </div>

        {/* 基础信息 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="label">
              <span className="label-text font-medium">标题</span>
            </label>
            <input
              type="text"
              className="input input-bordered w-full"
              value={formData.title}
              onChange={(e) => setFormData((prev) => ({ ...prev, title: e.target.value }))}
              placeholder="请输入标题"
              required
            />
          </div>

          <div>
            <label className="label">
              <span className="label-text font-medium">主分类</span>
            </label>
            <input
              type="text"
              className="input input-bordered w-full"
              value={formData.main_category}
              onChange={(e) => setFormData((prev) => ({ ...prev, main_category: e.target.value }))}
              placeholder="请输入主分类"
              required
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="label">
              <span className="label-text font-medium">子分类</span>
            </label>
            <input
              type="text"
              className="input input-bordered w-full"
              value={formData.sub_category}
              onChange={(e) => setFormData((prev) => ({ ...prev, sub_category: e.target.value }))}
              placeholder="请输入子分类"
              required
            />
          </div>

          <div>
            <label className="label">
              <span className="label-text font-medium">Doc选项</span>
            </label>
            <select
              className="select select-bordered w-full"
              value={formData.doc}
              onChange={(e) => setFormData((prev) => ({ ...prev, doc: e.target.value }))}
              required
            >
              <option value="">请选择Doc选项</option>
              {DOC_OPTIONS.map((option) => (
                <option key={option} value={option}>
                  {option}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div>
          <label className="label">
            <span className="label-text font-medium">描述</span>
          </label>
          <textarea
            className="textarea textarea-bordered w-full"
            value={formData.description}
            onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
            placeholder="请输入描述"
            rows={3}
            required
          />
        </div>

        {/* 内容区域 */}
        {formData.type === 101 && (
          <div>
            <label className="label">
              <span className="label-text font-medium">文本内容</span>
            </label>
            <textarea
              className="textarea textarea-bordered w-full"
              value={formData.content}
              onChange={(e) => setFormData((prev) => ({ ...prev, content: e.target.value }))}
              placeholder="请输入文本内容"
              rows={8}
              required
            />
          </div>
        )}

        {[102, 103, 104].includes(formData.type) && (
          <div>
            <label className="label">
              <span className="label-text font-medium">
                选择{MATERIAL_TYPES[formData.type as keyof typeof MATERIAL_TYPES]}
              </span>
            </label>
            <input
              id="fileInput"
              type="file"
              className="file-input file-input-bordered w-full"
              onChange={handleFileChange}
              accept={
                formData.type === 102 ? 'image/*'
                  : formData.type === 103 ? 'video/*'
                    : '*'
              }
              required
            />
          </div>
        )}

        {/* 文件预览 */}
        {filePreview && (
          <div>
            <label className="label">
              <span className="label-text font-medium">预览</span>
            </label>
            <div className="border rounded-lg p-4">
              <img src={filePreview} alt="预览" className="max-w-full h-auto max-h-64 mx-auto rounded" />
            </div>
          </div>
        )}

        {/* 提交按钮 */}
        <div className="flex justify-end gap-4">
          <button
            type="button"
            className="btn btn-outline"
            onClick={() => window.history.back()}
          >
            取消
          </button>
          <button
            type="submit"
            className={`btn btn-primary ${uploading ? 'loading' : ''}`}
            disabled={uploading}
          >
            {uploading ? '上传中...' : '上传素材'}
          </button>
        </div>
      </form>
    </div>
  )
}