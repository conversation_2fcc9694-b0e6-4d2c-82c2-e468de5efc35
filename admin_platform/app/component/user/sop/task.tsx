import Link from 'next/link'
import dayjs from 'dayjs'
import { ITask } from '../../../../../bot/service/moer/components/visualized_sop/visualized_sop_type'

export default function Task({ task }:{task:ITask}) {
  const data = task
  return <tr>
    <td className='whitespace-pre-wrap'>{data.name}</td>
    <td>{dayjs(data.sendTime).format('YYYY-MM-DD HH:mm:ss')}</td>
    <td>
      {data.sopId && (
        <Link href={`../../sop/${data.sopId}`}><button className='btn btn-info btn-soft'>detail</button></Link>
      )}
    </td>
  </tr>
}