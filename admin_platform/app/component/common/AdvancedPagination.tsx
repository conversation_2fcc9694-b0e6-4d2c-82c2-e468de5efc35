'use client'

import { useState } from 'react'

interface AdvancedPaginationProps {
  currentPage: number
  totalPages: number
  total: number
  pageSize?: number
  onPageChange: (page: number) => void
  onPageSizeChange?: (pageSize: number) => void
  pageSizeOptions?: number[]
  showPageSizeSelector?: boolean
  showQuickJumper?: boolean
}

export default function AdvancedPagination({
  currentPage,
  totalPages,
  total,
  pageSize = 20,
  onPageChange,
  onPageSizeChange,
  pageSizeOptions = [10, 20, 50, 100],
  showPageSizeSelector = false,
  showQuickJumper = true
}: AdvancedPaginationProps) {
  const [jumpPage, setJumpPage] = useState('')

  // 生成页码按钮数组
  const generatePageNumbers = () => {
    const pages: (number | string)[] = []
    const maxVisiblePages = 7 // 最多显示7个页码按钮

    if (totalPages <= 0) {
      return pages
    }

    if (totalPages <= maxVisiblePages) {
      // 如果总页数不超过最大显示数，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      // 复杂的页码显示逻辑
      if (currentPage <= 4) {
        // 当前页在前面
        for (let i = 1; i <= Math.min(5, totalPages); i++) {
          pages.push(i)
        }
        if (totalPages > 5) {
          pages.push('...')
          pages.push(totalPages)
        }
      } else if (currentPage >= totalPages - 3) {
        // 当前页在后面
        pages.push(1)
        if (totalPages > 5) {
          pages.push('...')
        }
        for (let i = Math.max(totalPages - 4, 1); i <= totalPages; i++) {
          pages.push(i)
        }
      } else {
        // 当前页在中间
        pages.push(1)
        pages.push('...')
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i)
        }
        pages.push('...')
        pages.push(totalPages)
      }
    }

    return pages
  }

  const handleJumpToPage = () => {
    const page = parseInt(jumpPage, 10)
    if (isNaN(page)) {
      alert('请输入有效的页码')
      return
    }
    if (page >= 1 && page <= totalPages) {
      onPageChange(page)
      setJumpPage('')
    } else {
      alert(`请输入1到${totalPages}之间的页码`)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleJumpToPage()
    }
  }

  if (totalPages <= 1) {
    return null
  }

  const pageNumbers = generatePageNumbers()

  return (
    <div className="flex flex-col items-center gap-4 mt-6">
      {/* 主分页控件 */}
      <div className="flex items-center gap-2">
        {/* 首页按钮 */}
        <button
          className="btn btn-sm"
          onClick={() => onPageChange(1)}
          disabled={currentPage === 1}
          title="首页"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
          </svg>
        </button>

        {/* 上一页按钮 */}
        <button
          className="btn btn-sm"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          上一页
        </button>

        {/* 页码按钮 */}
        <div className="join">
          {pageNumbers.map((page, index) => (
            <button
              key={index}
              className={`join-item btn btn-sm ${
                page === currentPage ? 'btn-active btn-primary' : ''
              } ${page === '...' ? 'btn-disabled' : ''}`}
              onClick={() => typeof page === 'number' && onPageChange(page)}
              disabled={page === '...'}
            >
              {page}
            </button>
          ))}
        </div>

        {/* 下一页按钮 */}
        <button
          className="btn btn-sm"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          下一页
        </button>

        {/* 末页按钮 */}
        <button
          className="btn btn-sm"
          onClick={() => onPageChange(totalPages)}
          disabled={currentPage === totalPages}
          title="末页"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
          </svg>
        </button>
      </div>

      {/* 辅助信息和控件 */}
      <div className="flex flex-wrap items-center justify-center gap-4 text-sm">
        {/* 分页信息 */}
        <div className="text-gray-500">
          第 {currentPage} 页，共 {totalPages} 页，总计 {total} 条记录
        </div>

        {/* 快速跳转 */}
        {showQuickJumper && (
          <div className="flex items-center gap-2">
            <span className="text-gray-500">跳转到</span>
            <input
              type="number"
              className="input input-sm input-bordered w-16 text-center"
              placeholder="页码"
              min={1}
              max={totalPages}
              value={jumpPage}
              onChange={(e) => {
                const value = e.target.value
                // 只允许数字输入
                if (value === '' || /^\d+$/.test(value)) {
                  setJumpPage(value)
                }
              }}
              onKeyDown={handleKeyDown}
            />
            <span className="text-gray-500">页</span>
            <button
              className="btn btn-sm btn-outline"
              onClick={handleJumpToPage}
              disabled={!jumpPage}
            >
              跳转
            </button>
          </div>
        )}

        {/* 每页条数选择器 */}
        {showPageSizeSelector && onPageSizeChange && (
          <div className="flex items-center gap-2">
            <span className="text-gray-500">每页</span>
            <select
              className="select select-sm select-bordered"
              value={pageSize}
              onChange={(e) => onPageSizeChange(parseInt(e.target.value, 10))}
            >
              {pageSizeOptions.map((size) => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
            <span className="text-gray-500">条</span>
          </div>
        )}
      </div>
    </div>
  )
}
