'use client'

import { useState, useEffect } from 'react'
import dayjs from 'dayjs'
import Link from 'next/link'
import { IPlannerTaskWithUser } from '@/app/api/planner'
import { FiChevronLeft, FiChevronRight, FiCalendar, FiClock, FiUser, FiEye, FiX } from 'react-icons/fi'

interface CalendarViewProps {
  tasks: IPlannerTaskWithUser[]
  loading: boolean
}

export function CalendarView({ tasks, loading }: CalendarViewProps) {
  const [selectedDate, setSelectedDate] = useState(dayjs())
  const [weekData, setWeekData] = useState<{ [key: string]: IPlannerTaskWithUser[] }>({})
  const [selectedTask, setSelectedTask] = useState<IPlannerTaskWithUser | null>(null)
  const [showDetailModal, setShowDetailModal] = useState(false)

  useEffect(() => {
    const weekStart = getStartOfWeek(selectedDate)
    const weekEnd = getEndOfWeek(selectedDate)

    // Group tasks by date
    const groupedTasks: { [key: string]: IPlannerTaskWithUser[] } = {}
    
    tasks.forEach(task => {
      const taskDate = dayjs(task.send_time)
      if (taskDate.isAfter(weekStart.subtract(1, 'day')) && taskDate.isBefore(weekEnd.add(1, 'day'))) {
        const dateKey = taskDate.format('YYYY-MM-DD')
        if (!groupedTasks[dateKey]) {
          groupedTasks[dateKey] = []
        }
        groupedTasks[dateKey].push(task)
      }
    })

    setWeekData(groupedTasks)
  }, [selectedDate, tasks])

  const getStartOfWeek = (date: dayjs.Dayjs) => {
    return date.startOf('week').add(1, 'day') // Start from Monday
  }

  const getEndOfWeek = (date: dayjs.Dayjs) => {
    return date.endOf('week').add(1, 'day') // End on Sunday
  }

  const goToPreviousWeek = () => {
    setSelectedDate(prev => prev.subtract(1, 'week'))
  }

  const goToNextWeek = () => {
    setSelectedDate(prev => prev.add(1, 'week'))
  }

  const goToToday = () => {
    setSelectedDate(dayjs())
  }

  const handleShowDetail = (task: IPlannerTaskWithUser) => {
    setSelectedTask(task)
    setShowDetailModal(true)
  }

  const handleCloseDetail = () => {
    setSelectedTask(null)
    setShowDetailModal(false)
  }

  const getWeekDays = () => {
    const startOfWeek = getStartOfWeek(selectedDate)
    const days = []
    for (let i = 0; i < 7; i++) {
      days.push(startOfWeek.add(i, 'day'))
    }
    return days
  }

  const getPriorityColor = (priority: number) => {
    if (priority >= 8) return 'badge-error'
    if (priority >= 5) return 'badge-warning'
    return 'badge-success'
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
      case '已完成':
        return 'badge-success'
      case 'in_progress':
      case '进行中':
        return 'badge-warning'
      case 'pending':
      case '待处理':
        return 'badge-info'
      default:
        return 'badge-ghost'
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <span className="loading loading-spinner loading-lg"></span>
      </div>
    )
  }

  const weekDays = getWeekDays()
  const weekStart = getStartOfWeek(selectedDate)
  const weekEnd = getEndOfWeek(selectedDate)

  return (
    <div className="p-6 bg-base-100 min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-center mb-8 gap-4">
        <div className="flex items-center gap-4">
          <FiCalendar className="text-2xl text-primary" />
          <h1 className="text-3xl font-bold text-base-content">任务日历</h1>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={goToPreviousWeek}
            className="btn btn-ghost btn-sm"
          >
            <FiChevronLeft className="text-lg" />
          </button>
          
          <button
            onClick={goToToday}
            className="btn btn-primary btn-sm"
          >
            今天
          </button>
          
          <button
            onClick={goToNextWeek}
            className="btn btn-ghost btn-sm"
          >
            <FiChevronRight className="text-lg" />
          </button>
        </div>
      </div>

      {/* Week Range Display */}
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold text-base-content">
          {weekStart.format('YYYY年MM月DD日')} - {weekEnd.format('MM月DD日')}
        </h2>
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-7 gap-4 calendar-fade-in">
        {weekDays.map((day, index) => {
          const dateKey = day.format('YYYY-MM-DD')
          const dayTasks = weekData[dateKey] || []
          const isToday = day.isSame(dayjs(), 'day')
          const isWeekend = day.day() === 0 || day.day() === 6

          return (
            <div
              key={dateKey}
              className={`
                card bg-base-200 shadow-sm border-2 min-h-[400px]
                ${isToday ? 'border-primary bg-primary/5' : 'border-base-300'}
                ${isWeekend ? 'bg-base-300/50' : ''}
              `}
            >
              <div className="card-body p-4">
                {/* Day Header */}
                <div className="flex justify-between items-center mb-3">
                  <div className="text-center">
                    <div className="text-sm font-medium text-base-content/70">
                      {day.format('ddd')}
                    </div>
                    <div className={`
                      text-lg font-bold
                      ${isToday ? 'text-primary' : 'text-base-content'}
                    `}>
                      {day.format('DD')}
                    </div>
                  </div>
                  <div className="badge badge-ghost badge-sm">
                    {dayTasks.length}
                  </div>
                </div>

                {/* Tasks */}
                <div className="space-y-2 flex-1">
                  {dayTasks.map((task, taskIndex) => (
                    <div
                      key={`${task.chat_id}-${task.id}-${taskIndex}`}
                      className="card card-compact bg-base-100 shadow-sm task-card cursor-pointer"
                    >
                      <div className="card-body p-3">
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-sm text-base-content leading-tight break-words">
                              {task.description}
                            </h4>
                            <div className="flex items-center gap-1 mt-1">
                              <FiUser className="text-xs text-base-content/60 flex-shrink-0" />
                              <Link
                                href={`/user/sop/${task.chat_id}`}
                                className="text-xs text-primary hover:text-primary-focus font-medium break-words"
                                title={task.user_name}
                              >
                                {task.user_name}
                              </Link>
                            </div>
                            <div className="flex items-center gap-1 mt-1">
                              <FiClock className="text-xs text-base-content/60 flex-shrink-0" />
                              <span className="text-xs text-base-content/60">
                                {dayjs(task.send_time).format('HH:mm')}
                              </span>
                            </div>
                          </div>
                          <div className="flex flex-col gap-1 flex-shrink-0">
                            <span className={`badge badge-xs ${getPriorityColor(task.priority)} font-bold`}>
                              P{task.priority}
                            </span>
                            <span className={`badge badge-xs ${getStatusColor(task.status)} text-xs`}>
                              {task.status}
                            </span>
                            <button
                              onClick={() => handleShowDetail(task)}
                              className="btn btn-xs btn-ghost btn-outline hover:btn-primary"
                              title="查看详情"
                            >
                              <FiEye className="text-xs" />
                            </button>
                          </div>
                        </div>

                        {task.overall_goal && (
                          <div className="mt-2 pt-2 border-t border-base-300">
                            <p className="text-xs text-base-content/70 break-words" title={task.overall_goal}>
                              目标: {task.overall_goal}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  {dayTasks.length === 0 && (
                    <div className="flex items-center justify-center h-20 text-base-content/40">
                      <span className="text-sm">无任务</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Summary */}
      <div className="mt-8 stats shadow w-full">
        <div className="stat">
          <div className="stat-title">本周任务总数</div>
          <div className="stat-value text-primary">
            {Object.values(weekData).flat().length}
          </div>
        </div>
        <div className="stat">
          <div className="stat-title">高优先级任务</div>
          <div className="stat-value text-error">
            {Object.values(weekData).flat().filter(task => task.priority >= 8).length}
          </div>
        </div>
        <div className="stat">
          <div className="stat-title">已完成任务</div>
          <div className="stat-value text-success">
            {Object.values(weekData).flat().filter(task => 
              task.status.toLowerCase().includes('completed') || 
              task.status.includes('已完成')
            ).length}
          </div>
        </div>
      </div>

      {/* Detail Modal */}
      {showDetailModal && selectedTask && (
        <div className="modal modal-open">
          <div className="modal-box max-w-2xl">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-bold text-lg">任务详情</h3>
              <button
                onClick={handleCloseDetail}
                className="btn btn-sm btn-circle btn-ghost"
              >
                <FiX className="text-lg" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="label">
                  <span className="label-text font-semibold">用户</span>
                </label>
                <div className="flex items-center gap-2">
                  <FiUser className="text-primary" />
                  <Link
                    href={`/user/sop/${selectedTask.chat_id}`}
                    className="link link-primary font-medium"
                  >
                    {selectedTask.user_name}
                  </Link>
                  <span className="text-xs text-gray-500">({selectedTask.user_id})</span>
                </div>
              </div>

              <div>
                <label className="label">
                  <span className="label-text font-semibold">任务描述</span>
                </label>
                <div className="bg-base-200 p-3 rounded-lg">
                  <p className="text-sm break-words">{selectedTask.description}</p>
                </div>
              </div>

              <div>
                <label className="label">
                  <span className="label-text font-semibold">总体目标</span>
                </label>
                <div className="bg-base-200 p-3 rounded-lg">
                  <p className="text-sm break-words">{selectedTask.overall_goal}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="label">
                    <span className="label-text font-semibold">发送时间</span>
                  </label>
                  <div className="flex items-center gap-2">
                    <FiClock className="text-primary" />
                    <span className="text-sm">{dayjs(selectedTask.send_time).format('YYYY-MM-DD HH:mm')}</span>
                  </div>
                </div>

                <div>
                  <label className="label">
                    <span className="label-text font-semibold">优先级</span>
                  </label>
                  <span className={`badge ${getPriorityColor(selectedTask.priority)} font-bold`}>
                    P{selectedTask.priority}
                  </span>
                </div>
              </div>

              <div>
                <label className="label">
                  <span className="label-text font-semibold">状态</span>
                </label>
                <span className={`badge ${getStatusColor(selectedTask.status)}`}>
                  {selectedTask.status}
                </span>
              </div>
            </div>

            <div className="modal-action">
              <Link
                href={`/user/sop/${selectedTask.chat_id}`}
                className="btn btn-primary"
              >
                查看用户SOP
              </Link>
              <button onClick={handleCloseDetail} className="btn">
                关闭
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
