'use server'
import { AliyunCredentials } from '../../../bot/lib/cer'
import { FreeSpiritOss } from '../../../bot/model/oss/oss'


export async function putObjectStream(name: string, file: File, basePath:string = 'rag_file'):Promise<string> {
  const bucket = new FreeSpiritOss('static')

  const buffer = await file.arrayBuffer()
  const readableBuffer = Buffer.from(buffer)
  const res = await bucket.putObject(`${basePath}/${name}`, readableBuffer)
  return res.url
}

export async function deleteObjectByName(name: string, basePath: string = 'rag_file'): Promise<void> {
  const bucket = new FreeSpiritOss('static')
  const objectPath = `${basePath}/${name}`

  try {
    await bucket.deleteObject(objectPath)
  } catch (error) {
    console.error(`删除OSS文件失败: ${objectPath}`, error)
    throw error
  }
}

AliyunCredentials.initialize({
  region: 'cn-hangzhou',
  accountId: '****************',
  accessKeyId: 'LTAI5tRVPxefUtgyLCfc5f69',
  secretAccessKey: '******************************',
})