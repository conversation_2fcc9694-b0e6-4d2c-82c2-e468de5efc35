'use client'
import { use } from 'react'
import { queryChatHistoryByChatId } from '@/app/api/chat_history'
import { queryLogByChatId } from '@/app/api/log_store'
import { queryChatById } from '@/app/api/chat'
import { queryAccounts } from '@/app/api/account'
import { ChatHistory } from '@/app/component/user/chat_history'

export default function Page({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params)
  return <ChatHistory
    id={id}
    queryChatHistoryByChatId={queryChatHistoryByChatId}
    queryLogByChatId={queryLogByChatId}
    queryChatById={queryChatById}
    queryAccounts={queryAccounts}
    langsmithProjectId='6f6fbd77-99a2-4c1f-9a9c-c76fbc092375'
  />
}