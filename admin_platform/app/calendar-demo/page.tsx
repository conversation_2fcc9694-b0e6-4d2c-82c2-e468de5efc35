'use client'

import { useState, useEffect } from 'react'
import { CalendarView } from '@/app/component/planner/CalendarView'
import { IPlannerTaskWithUser } from '@/app/api/planner'
import dayjs from 'dayjs'

// Mock data for demonstration
const mockTasks: IPlannerTaskWithUser[] = [
  {
    id: '1',
    chat_id: 'user1',
    round_id: 'round1',
    overall_goal: '提升客户满意度',
    description: '与客户进行产品需求沟通会议',
    status: 'pending',
    priority: 8,
    created_at: new Date(),
    send_time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    user_name: '张三',
    user_id: 'user1'
  },
  {
    id: '2',
    chat_id: 'user2',
    round_id: 'round2',
    overall_goal: '完成项目交付',
    description: '项目进度评审会议',
    status: 'in_progress',
    priority: 6,
    created_at: new Date(),
    send_time: dayjs().add(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
    user_name: '李四',
    user_id: 'user2'
  },
  {
    id: '3',
    chat_id: 'user3',
    round_id: 'round3',
    overall_goal: '团队协作优化',
    description: '团队建设活动策划',
    status: 'completed',
    priority: 4,
    created_at: new Date(),
    send_time: dayjs().add(2, 'day').format('YYYY-MM-DD HH:mm:ss'),
    user_name: '王五',
    user_id: 'user3'
  },
  {
    id: '4',
    chat_id: 'user4',
    round_id: 'round4',
    overall_goal: '技术能力提升',
    description: '新技术培训课程安排',
    status: 'pending',
    priority: 7,
    created_at: new Date(),
    send_time: dayjs().add(3, 'day').format('YYYY-MM-DD HH:mm:ss'),
    user_name: '赵六',
    user_id: 'user4'
  },
  {
    id: '5',
    chat_id: 'user5',
    round_id: 'round5',
    overall_goal: '业务流程优化',
    description: '流程梳理和改进讨论',
    status: 'in_progress',
    priority: 5,
    created_at: new Date(),
    send_time: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
    user_name: '孙七',
    user_id: 'user5'
  },
  {
    id: '6',
    chat_id: 'user6',
    round_id: 'round6',
    overall_goal: '客户关系维护',
    description: '重要客户拜访',
    status: 'pending',
    priority: 9,
    created_at: new Date(),
    send_time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    user_name: '周八',
    user_id: 'user6'
  },
  {
    id: '7',
    chat_id: 'user7',
    round_id: 'round7',
    overall_goal: '产品功能完善',
    description: '用户反馈收集和分析',
    status: 'completed',
    priority: 6,
    created_at: new Date(),
    send_time: dayjs().add(4, 'day').format('YYYY-MM-DD HH:mm:ss'),
    user_name: '吴九',
    user_id: 'user7'
  },
  {
    id: '8',
    chat_id: 'user8',
    round_id: 'round8',
    overall_goal: '市场推广策略',
    description: '营销活动效果评估',
    status: 'pending',
    priority: 7,
    created_at: new Date(),
    send_time: dayjs().add(5, 'day').format('YYYY-MM-DD HH:mm:ss'),
    user_name: '郑十',
    user_id: 'user8'
  }
]

export default function CalendarDemoPage() {
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="min-h-screen bg-base-100">
      <div className="container mx-auto">
        <div className="text-center py-8">
          <h1 className="text-4xl font-bold text-primary mb-4">
            🎉 Google Calendar 风格界面演示
          </h1>
          <p className="text-lg text-base-content/70 mb-8">
            全新的任务管理界面，美观且功能强大
          </p>
        </div>
        
        <CalendarView tasks={mockTasks} loading={loading} />
        
        <div className="text-center py-8">
          <div className="alert alert-info max-w-2xl mx-auto">
            <div>
              <h3 className="font-bold">功能特点</h3>
              <div className="text-sm mt-2 space-y-1">
                <p>✅ 美观的 Google Calendar 风格设计</p>
                <p>✅ 响应式布局，支持移动端</p>
                <p>✅ 周视图导航，支持上一周/下一周切换</p>
                <p>✅ 任务优先级和状态标识</p>
                <p>✅ 平滑的动画效果</p>
                <p>✅ 统计信息展示</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
