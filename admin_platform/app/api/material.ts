'use server'

import { PrismaMongoClient } from '../../../bot/model/mongodb/prisma'
import { RAGHelper } from '../../../bot/model/rag/rag'
import ElasticSearchService from '../../../bot/model/elastic_search/elastic_search'
import { Document } from 'langchain/document'
import { deleteObjectByName } from '../rag/aliyunoss'

const RAG_INDEX = 'moer_material'
const OSS_MATERIAL_PATH = 'moer/material'

export interface Material {
  id: string
  type: number
  title: string
  description: string
  main_category: string
  sub_category: string
  doc: string
  data: any
  enable: boolean
  es_id: string
  created_at?: Date
}

export interface MaterialQueryParams {
  page?: number
  pageSize?: number
  type?: number
  main_category?: string
  sub_category?: string
  doc?: string
}

export async function queryMaterials(params: MaterialQueryParams = {}) {
  const { page = 1, pageSize = 20, type, main_category, sub_category, doc } = params
  const skip = (page - 1) * pageSize

  try {
    const whereCondition: any = {}
    if (type !== undefined) {
      whereCondition.type = type
    }
    if (main_category) {
      whereCondition.main_category = main_category
    }
    if (sub_category) {
      whereCondition.sub_category = sub_category
    }
    if (doc) {
      whereCondition.doc = doc
    }

    const [materials, total] = await Promise.all([
      PrismaMongoClient.getInstance().material.findMany({
        where: whereCondition,
        skip,
        take: pageSize,
        orderBy: {
          id: 'desc'  // 使用id排序代替created_at
        }
      }),
      PrismaMongoClient.getInstance().material.count({
        where: whereCondition
      })
    ])

    return {
      materials,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  } catch (error) {
    console.error('Error querying materials:', error)
    throw error
  }
}

export async function updateMaterial(id: string, updates: { title?: string; description?: string; doc?: string; main_category?: string; sub_category?: string }) {
  try {
    // 获取当前素材信息
    const currentMaterial = await PrismaMongoClient.getInstance().material.findUnique({
      where: { id }
    })

    if (!currentMaterial) {
      throw new Error('素材不存在')
    }

    // 更新MongoDB中的素材
    const material = await PrismaMongoClient.getInstance().material.update({
      where: { id },
      data: updates
    })

    // 如果素材已启用且有es_id，需要更新RAG中的文档
    if (material.enable && material.es_id) {
      try {
        // 删除旧的ES文档
        await ElasticSearchService.deleteDocuments(RAG_INDEX, [material.es_id])

        // 重新添加更新后的文档
        const [newEsId] = await RAGHelper.addDocuments(RAG_INDEX, [{
          pageContent: updates.title || material.title,
          metadata: {
            title: updates.title || material.title,
            description: updates.description || material.description,
            source_id: material.id,
            doc: updates.doc || material.doc,
            category: updates.main_category || material.main_category
          }
        }])

        // 更新MongoDB中的es_id
        await PrismaMongoClient.getInstance().material.update({
          where: { id },
          data: { es_id: newEsId }
        })

        // 更新返回的material对象
        material.es_id = newEsId
      } catch (error) {
        console.error('更新RAG文档失败:', error)
        // 即使RAG更新失败，也返回更新后的material
      }
    }

    return material
  } catch (error) {
    console.error('Error updating material:', error)
    throw error
  }
}

export async function toggleMaterialEnable(id: string, enable: boolean) {
  try {
    // 获取素材信息
    const material = await PrismaMongoClient.getInstance().material.findUnique({
      where: { id }
    })

    if (!material) {
      throw new Error('素材不存在')
    }

    if (enable) {
      // 启用素材
      // 如果已经有es_id，说明已经在ES中，直接返回
      if (material.es_id && material.enable) {
        return material
      }

      // 添加到ES
      const docs = [{
        pageContent: material.title,
        metadata: {
          title: material.title,
          description: material.description,
          source_id: material.id,
          doc: material.doc,
          category: material.main_category
        }
      }] as Document[]

      const [esId] = await RAGHelper.addDocuments(RAG_INDEX, docs)

      // 由于RAGHelper.addDocuments不返回ID，我们需要生成一个唯一标识
      // 通常使用材料ID作为ES文档ID

      // 更新MongoDB中的es_id和enable状态
      const updatedMaterial = await PrismaMongoClient.getInstance().material.update({
        where: { id },
        data: {
          es_id: esId,
          enable: true
        }
      })

      return updatedMaterial
    } else {
      // 禁用素材
      // 从ES中删除
      if (material.es_id) {
        try {
          await ElasticSearchService.deleteDocuments(RAG_INDEX, [material.es_id])
        } catch (error) {
          console.error('从ES删除文档失败:', error)
          // 继续更新MongoDB，即使ES删除失败
        }
      }

      // 更新MongoDB，清空es_id并设置enable为false
      const updatedMaterial = await PrismaMongoClient.getInstance().material.update({
        where: { id },
        data: {
          es_id: '',
          enable: false
        }
      })

      return updatedMaterial
    }
  } catch (error) {
    console.error('Error toggling material enable:', error)
    throw error
  }
}

export async function getMaterialTypes() {
  try {
    const materials = await PrismaMongoClient.getInstance().material.groupBy({
      by: ['type'],
      _count: {
        type: true
      }
    })

    return materials.map((item) => ({
      type: item.type,
      count: item._count.type
    }))
  } catch (error) {
    console.error('Error getting material types:', error)
    throw error
  }
}

export async function getMainCategories() {
  try {
    const materials = await PrismaMongoClient.getInstance().material.groupBy({
      by: ['main_category'],
      _count: {
        main_category: true
      }
    })

    return materials.map((item) => ({
      main_category: item.main_category,
      count: item._count.main_category
    })).sort((a, b) => b.count - a.count)
  } catch (error) {
    console.error('Error getting main categories:', error)
    throw error
  }
}

export async function getSubCategories(mainCategory?: string) {
  try {
    const whereCondition: any = {}
    if (mainCategory) {
      whereCondition.main_category = mainCategory
    }

    const materials = await PrismaMongoClient.getInstance().material.groupBy({
      by: ['sub_category'],
      where: whereCondition,
      _count: {
        sub_category: true
      }
    })

    return materials.map((item) => ({
      sub_category: item.sub_category,
      count: item._count.sub_category
    })).sort((a, b) => b.count - a.count) // 按使用频率排序
  } catch (error) {
    console.error('Error getting sub categories:', error)
    throw error
  }
}

export async function deleteMaterial(id: string) {
  // 定义多媒体类型：102(图片), 103(视频), 104(文件)
  const MULTIMEDIA_TYPES = [102, 103, 104]

  try {
    // 获取素材信息
    const material = await PrismaMongoClient.getInstance().material.findUnique({
      where: { id }
    })

    if (!material) {
      throw new Error('素材不存在')
    }

    // 如果是多媒体类型且有文件数据，从OSS中删除
    if (MULTIMEDIA_TYPES.includes(material.type) && material.data && typeof material.data === 'object') {
      try {
        const data = material.data as any
        // 从文件URL中提取文件名
        if (data.url) {
          const fileName = data.url.split('/').pop()
          if (fileName) {
            await deleteObjectByName(fileName, OSS_MATERIAL_PATH)
          }
        }
      } catch (error) {
        console.error('从OSS删除文件失败:', error)
        // 继续删除数据，即使OSS删除失败
      }
    }

    // 如果有es_id，从ES中删除
    if (material.es_id) {
      try {
        await ElasticSearchService.deleteDocuments(RAG_INDEX, [material.es_id])
      } catch (error) {
        console.error('删除ES文档失败:', error)
        // 继续删除MongoDB数据，即使ES删除失败
      }
    }

    // 删除MongoDB中的素材
    await PrismaMongoClient.getInstance().material.delete({
      where: { id }
    })
  } catch (error) {
    console.error('Error deleting material:', error)
    throw error
  }
}

export async function getDocOptions() {
  try {
    const materials = await PrismaMongoClient.getInstance().material.groupBy({
      by: ['doc'],
      _count: {
        doc: true
      }
    })

    return materials.map((item) => ({
      doc: item.doc,
      count: item._count.doc
    })).sort((a, b) => b.count - a.count)
  } catch (error) {
    console.error('Error getting doc options:', error)
    throw error
  }
}

export async function checkMaterialsExistence(ids: string[]) {
  try {
    const results: Record<string, boolean> = {}

    // 批量查询已存在的素材
    const existingMaterials = await PrismaMongoClient.getInstance().material.findMany({
      where: {
        id: {
          in: ids
        }
      },
      select: {
        id: true
      }
    })

    // 构建存在状态映射
    const existingIds = new Set(existingMaterials.map((m) => m.id))
    ids.forEach((id) => {
      results[id] = existingIds.has(id)
    })

    return results
  } catch (error) {
    console.error('Error checking materials existence:', error)
    throw error
  }
}

export async function createMaterial(data: {
  type: number
  title: string
  description: string
  main_category: string
  sub_category: string
  data: any
  doc?: string
}) {
  try {
    const material = await PrismaMongoClient.getInstance().material.create({
      data: {
        type: data.type,
        title: data.title,
        description: data.description,
        main_category: data.main_category,
        sub_category: data.sub_category,
        doc: data.doc || '',
        data: data.data,
        enable: false,
        es_id: ''
      }
    })
    return material
  } catch (error) {
    console.error('Error creating material:', error)
    throw error
  }
}