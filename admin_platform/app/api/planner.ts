'use server'

import { Queue } from 'bullmq'
import { RedisDB } from '../../../bot/model/redis/redis'
import { PrismaMongoClient } from '../../../bot/model/mongodb/prisma'
import { JuziAPI } from '../../../bot/lib/juzi/api'
import { getUserId } from '../../../bot/config/chat_id'
import dayjs from 'dayjs'
import { ChatDB } from '../../../bot/service/moer/database/chat'

interface IPlannerScheduleTask {
  id: string
  chat_id: string
  round_id: string
  overall_goal: string
  description: string
  status: string
  priority: number
  created_at: Date
  send_time: string
}

export interface IPlannerTaskWithUser {
  id: string
  chat_id: string
  round_id: string
  overall_goal: string
  description: string
  status: string
  priority: number
  created_at: Date
  send_time: Date
  user_name: string
  user_id: string
}

/**
 * 获取所有 planner 规划的任务
 */
export async function getAllPlannerTasks(): Promise<IPlannerTaskWithUser[]> {
  try {
    // 获取所有 bot 配置
    const mongoConfigClient = PrismaMongoClient.getConfigInstance()
    const botConfigs = await mongoConfigClient.config.findMany({
      where: { enterpriseName: 'moer' },
      select: { wechatId: true }
    })

    const allTasks: IPlannerTaskWithUser[] = []

    // 遍历所有 bot，获取其 planner 任务
    for (const botConfig of botConfigs) {
      const botId = botConfig.wechatId
      if (!botId) continue

      try {
        const plannerQueue = new Queue<IPlannerScheduleTask>(`moer-planner-sop-${botId}`, {
          connection: RedisDB.getInstance()
        })

        const jobs = await plannerQueue.getDelayed()

        for (const job of jobs) {
          const task = job.data
          const userId = getUserId(task.chat_id)

          // 获取客户信息
          let userName = userId
          try {
            const chat = await ChatDB.getById(task.chat_id)
            if (chat) {
              userName = chat.contact.wx_name
            }

          } catch (error) {
            console.warn(`Failed to get customer info for userId: ${userId}`, error)
          }

          allTasks.push({
            ...task,
            send_time: dayjs(task.send_time).toDate(),
            user_name: userName,
            user_id: userId
          })
        }
      } catch (error) {
        console.error(`Failed to get planner tasks for bot ${botId}:`, error)
      }
    }

    // 按发送时间排序
    return allTasks.sort((a, b) => a.send_time.getTime() - b.send_time.getTime())
  } catch (error) {
    console.error('Failed to get all planner tasks:', error)
    throw new Error('获取 planner 任务失败')
  }
}

/**
 * 根据 chat_id 获取特定客户的 planner 任务
 */
export async function getPlannerTasksByChatId(chatId: string): Promise<IPlannerTaskWithUser[]> {
  try {
    const allTasks = await getAllPlannerTasks()
    return allTasks.filter((task) => task.chat_id === chatId)
  } catch (error) {
    console.error(`Failed to get planner tasks for chatId ${chatId}:`, error)
    throw new Error('获取客户 planner 任务失败')
  }
}
